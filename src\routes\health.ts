import { Router, Request, Response } from "express";
import { AgentRegistry } from "../core/AgentRegistry";
import { logger } from "../utils/logger";

const router = Router();

// Basic health check
router.get("/", (req: Request, res: Response) => {
  res.json({
    status: "healthy",
    timestamp: new Date().toISOString(),
    service: "langchain-agents-firebase",
    version: "1.0.0"
  });
});

// Detailed health check
router.get("/detailed", (req: Request, res: Response) => {
  try {
    const registry = AgentRegistry.getInstance();
    const stats = registry.getStats();
    
    const healthData = {
      status: "healthy",
      timestamp: new Date().toISOString(),
      service: "langchain-agents-firebase",
      version: "1.0.0",
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      agents: stats,
      environment: {
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch
      }
    };

    res.json(healthData);
  } catch (error) {
    logger.error("Health check failed", {
      error: error instanceof Error ? error.message : error
    });
    
    res.status(500).json({
      status: "unhealthy",
      timestamp: new Date().toISOString(),
      error: "Internal health check error"
    });
  }
});

// Readiness probe
router.get("/ready", (req: Request, res: Response) => {
  // Check if all required services are available
  const checks = {
    firebase: true, // Assume Firebase is ready if we got this far
    agents: true,   // Agent system is ready
    memory: process.memoryUsage().heapUsed < 1024 * 1024 * 1024 // Less than 1GB heap used
  };

  const isReady = Object.values(checks).every(check => check);
  
  res.status(isReady ? 200 : 503).json({
    status: isReady ? "ready" : "not ready",
    timestamp: new Date().toISOString(),
    checks
  });
});

// Liveness probe
router.get("/live", (req: Request, res: Response) => {
  res.json({
    status: "alive",
    timestamp: new Date().toISOString(),
    pid: process.pid
  });
});

export { router as healthRoutes };
