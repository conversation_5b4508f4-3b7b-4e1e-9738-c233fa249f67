import * as functions from "firebase-functions";
import { ReactAgent } from "../agents/ReactAgent";
import { AgentType } from "../types/agent";
import { logger } from "../utils/logger";
import { validateConfig } from "../config/environment";

// Validate environment on cold start
validateConfig();

export const reactAgent = functions
  .region("us-central1")
  .runWith({
    timeoutSeconds: 300,
    memory: "1GB",
  })
  .https
  .onCall(async (data, context) => {
    try {
      // Validate authentication
      if (!context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Authentication required"
        );
      }

      // Validate input
      if (!data.message || typeof data.message !== "string") {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Message is required and must be a string"
        );
      }

      const { message, config: agentConfig = {}, context: agentContext = {} } = data;

      logger.info("ReAct agent function called", {
        userId: context.auth.uid,
        messageLength: message.length,
        config: agentConfig
      });

      // Create agent configuration
      const config = {
        type: AgentType.REACT,
        model: agentConfig.model || "gpt-4",
        temperature: agentConfig.temperature || 0.7,
        maxIterations: agentConfig.maxIterations || 10,
        timeoutMs: agentConfig.timeoutMs || 30000,
        systemPrompt: agentConfig.systemPrompt,
        verbose: agentConfig.verbose || false,
        tools: agentConfig.tools || [],
        ...agentConfig
      };

      // Create and execute agent
      const agent = new ReactAgent(config);
      
      const result = await agent.execute({
        message,
        context: {
          sessionId: agentContext.sessionId || `react_${context.auth.uid}_${Date.now()}`,
          userId: context.auth.uid,
          conversationId: agentContext.conversationId,
          metadata: agentContext.metadata
        }
      });

      logger.info("ReAct agent execution completed", {
        userId: context.auth.uid,
        agentId: agent.id,
        success: result.result.success,
        executionTime: result.result.executionTime,
        steps: result.result.steps?.length || 0
      });

      return {
        success: true,
        data: {
          response: result.response,
          executionResult: result.result,
          intermediateSteps: result.result.metadata?.intermediateSteps,
          steps: result.result.steps
        }
      };

    } catch (error) {
      logger.error("ReAct agent function error", {
        userId: context.auth?.uid,
        error: error instanceof Error ? error.message : error
      });

      if (error instanceof functions.https.HttpsError) {
        throw error;
      }

      throw new functions.https.HttpsError(
        "internal",
        "An error occurred while processing your request"
      );
    }
  });
