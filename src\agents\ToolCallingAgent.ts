import { Chat<PERSON>penAI } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { AgentExecutor, createToolCallingAgent } from "langchain/agents";
import { ChatPromptTemplate } from "@langchain/core/prompts";
import { BaseAgent } from "../core/BaseAgent";
import { 
  AgentConfig, 
  AgentInput, 
  AgentOutput, 
  AgentContext,
  AgentStep,
  AgentType 
} from "../types/agent";
import { config } from "../config/environment";
import { logger } from "../utils/logger";

export class ToolCallingAgent extends BaseAgent {
  private executor?: AgentExecutor;
  private model: ChatOpenAI | ChatAnthropic;

  constructor(agentConfig: AgentConfig) {
    super({ ...agentConfig, type: AgentType.TOOL_CALLING });
    this.initializeModel();
  }

  private initializeModel(): void {
    const modelName = this._config.model.toLowerCase();
    
    if (modelName.includes("gpt") || modelName.includes("openai")) {
      this.model = new ChatOpenAI({
        modelName: this._config.model,
        temperature: this._config.temperature,
        openAIApiKey: config.openaiApiKey,
        maxTokens: 2000,
        timeout: this._config.timeoutMs
      });
    } else if (modelName.includes("claude") || modelName.includes("anthropic")) {
      this.model = new ChatAnthropic({
        modelName: this._config.model,
        temperature: this._config.temperature,
        anthropicApiKey: config.anthropicApiKey,
        maxTokens: 2000,
        timeout: this._config.timeoutMs
      });
    } else {
      throw new Error(`Unsupported model: ${this._config.model}`);
    }
  }

  protected async executeInternal(input: AgentInput, context: AgentContext): Promise<AgentOutput> {
    this.validateInput(input);
    
    const startTime = Date.now();
    const steps: AgentStep[] = [];
    let stepNumber = 1;

    try {
      // Initialize executor if not already done
      if (!this.executor) {
        await this.initializeExecutor();
      }

      logger.info("Starting Tool-calling agent execution", {
        agentId: this.id,
        sessionId: context.sessionId,
        model: this._config.model,
        toolCount: this._config.tools?.length || 0
      });

      // Add analysis step
      steps.push(this.createStep(
        stepNumber++,
        "analyze",
        input.message,
        undefined,
        "Analyzing request to determine which tools to use",
        0,
        0
      ));

      // Execute the agent
      const result = await this.executor!.invoke({
        input: input.message,
        chat_history: input.history || []
      });

      // Add tool execution steps
      if (result.intermediateSteps) {
        for (const [index, step] of result.intermediateSteps.entries()) {
          const toolName = step.action?.tool || "unknown_tool";
          const toolInput = step.action?.toolInput || step.action?.input;
          
          steps.push(this.createStep(
            stepNumber++,
            "tool_call",
            toolInput,
            step.observation,
            `Called tool: ${toolName}`,
            0,
            0
          ));
        }
      }

      const executionTime = Date.now() - startTime;
      
      // Add synthesis step
      steps.push(this.createStep(
        stepNumber++,
        "synthesize",
        undefined,
        result.output,
        "Synthesizing tool results into final response",
        result.tokensUsed || 0,
        executionTime
      ));

      const executionResult = this.createSuccessResult(
        result.output,
        steps,
        result.tokensUsed || 0,
        executionTime,
        {
          toolsUsed: result.intermediateSteps?.map((step: any) => step.action?.tool).filter(Boolean) || [],
          intermediateSteps: result.intermediateSteps,
          agentType: "tool-calling"
        }
      );

      logger.info("Tool-calling agent execution completed", {
        agentId: this.id,
        sessionId: context.sessionId,
        success: true,
        executionTime,
        tokensUsed: result.tokensUsed,
        toolsUsed: executionResult.metadata?.toolsUsed?.length || 0
      });

      return {
        response: result.output,
        context,
        result: executionResult
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      
      logger.error("Tool-calling agent execution failed", {
        agentId: this.id,
        sessionId: context.sessionId,
        error: errorMessage,
        executionTime
      });

      throw error;
    }
  }

  private async initializeExecutor(): Promise<void> {
    try {
      // Create the prompt template
      const prompt = ChatPromptTemplate.fromMessages([
        ["system", this._config.systemPrompt || `You are a helpful assistant that can use tools to answer questions and complete tasks.
        
Use the available tools when needed to provide accurate and helpful responses. Always explain what you're doing and why you're using specific tools.

Available tools: {tools}

When using tools:
1. Think about which tool is most appropriate for the task
2. Use the tool with the correct input format
3. Interpret the results and provide a clear response to the user

If you cannot complete a task with the available tools, explain what you would need to accomplish it.`],
        ["placeholder", "{chat_history}"],
        ["human", "{input}"],
        ["placeholder", "{agent_scratchpad}"]
      ]);

      // Create the tool-calling agent
      const agent = await createToolCallingAgent({
        llm: this.model,
        tools: this._config.tools || [],
        prompt: prompt
      });

      // Create the executor
      this.executor = new AgentExecutor({
        agent,
        tools: this._config.tools || [],
        maxIterations: this._config.maxIterations,
        verbose: this._config.verbose,
        returnIntermediateSteps: true,
        handleParsingErrors: true
      });

      logger.info("Tool-calling agent executor initialized", {
        agentId: this.id,
        toolCount: this._config.tools?.length || 0,
        maxIterations: this._config.maxIterations
      });

    } catch (error) {
      logger.error("Failed to initialize Tool-calling agent executor", {
        agentId: this.id,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  public updateConfig(newConfig: Partial<AgentConfig>): void {
    super.updateConfig(newConfig);
    
    // Reinitialize model if model-related config changed
    if (newConfig.model || newConfig.temperature) {
      this.initializeModel();
      this.executor = undefined; // Force re-initialization
    }
    
    // Reinitialize executor if tools, iterations, or system prompt changed
    if (newConfig.tools || newConfig.maxIterations || newConfig.systemPrompt) {
      this.executor = undefined; // Force re-initialization
    }
  }
}
