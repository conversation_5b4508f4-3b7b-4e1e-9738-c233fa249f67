import { IAgentRegistry, IAgent } from "../types/agent";
import { logger } from "../utils/logger";

export class AgentRegistry implements IAgentRegistry {
  private static instance: AgentRegistry;
  private agents: Map<string, IAgent> = new Map();
  private sessionAgents: Map<string, Set<string>> = new Map();
  private cleanupInterval?: NodeJS.Timeout;

  private constructor() {
    // Start cleanup process every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.performCleanup();
    }, 5 * 60 * 1000);
  }

  public static getInstance(): AgentRegistry {
    if (!AgentRegistry.instance) {
      AgentRegistry.instance = new AgentRegistry();
    }
    return AgentRegistry.instance;
  }

  public register(agent: IAgent): void {
    this.agents.set(agent.id, agent);
    logger.info(`Agent registered`, { 
      agentId: agent.id, 
      type: agent.type,
      totalAgents: this.agents.size 
    });
  }

  public unregister(agentId: string): void {
    const agent = this.agents.get(agentId);
    if (agent) {
      // Stop the agent if it's running
      agent.stop().catch(error => {
        logger.error(`Error stopping agent during unregistration`, {
          agentId,
          error: error instanceof Error ? error.message : error
        });
      });

      this.agents.delete(agentId);
      
      // Remove from session mappings
      for (const [sessionId, agentIds] of this.sessionAgents.entries()) {
        agentIds.delete(agentId);
        if (agentIds.size === 0) {
          this.sessionAgents.delete(sessionId);
        }
      }

      logger.info(`Agent unregistered`, { 
        agentId, 
        totalAgents: this.agents.size 
      });
    }
  }

  public get(agentId: string): IAgent | undefined {
    return this.agents.get(agentId);
  }

  public getBySession(sessionId: string): IAgent[] {
    const agentIds = this.sessionAgents.get(sessionId);
    if (!agentIds) {
      return [];
    }

    const agents: IAgent[] = [];
    for (const agentId of agentIds) {
      const agent = this.agents.get(agentId);
      if (agent) {
        agents.push(agent);
      }
    }

    return agents;
  }

  public associateWithSession(agentId: string, sessionId: string): void {
    if (!this.sessionAgents.has(sessionId)) {
      this.sessionAgents.set(sessionId, new Set());
    }
    this.sessionAgents.get(sessionId)!.add(agentId);
  }

  public async cleanup(): Promise<void> {
    logger.info("Starting agent registry cleanup", { 
      totalAgents: this.agents.size,
      totalSessions: this.sessionAgents.size 
    });

    const stopPromises: Promise<void>[] = [];
    
    for (const agent of this.agents.values()) {
      stopPromises.push(
        agent.stop().catch(error => {
          logger.error(`Error stopping agent during cleanup`, {
            agentId: agent.id,
            error: error instanceof Error ? error.message : error
          });
        })
      );
    }

    await Promise.all(stopPromises);
    
    this.agents.clear();
    this.sessionAgents.clear();

    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = undefined;
    }

    logger.info("Agent registry cleanup completed");
  }

  private async performCleanup(): Promise<void> {
    const idleAgents: string[] = [];
    
    for (const [agentId, agent] of this.agents.entries()) {
      if (agent.getStatus() === "idle" || agent.getStatus() === "completed") {
        idleAgents.push(agentId);
      }
    }

    // Remove idle agents older than 30 minutes
    const thirtyMinutesAgo = Date.now() - (30 * 60 * 1000);
    
    for (const agentId of idleAgents) {
      // In a real implementation, you'd check the agent's last activity time
      // For now, we'll keep all agents to avoid premature cleanup
    }

    if (idleAgents.length > 100) {
      // If we have too many idle agents, clean up the oldest ones
      const agentsToRemove = idleAgents.slice(0, idleAgents.length - 50);
      for (const agentId of agentsToRemove) {
        this.unregister(agentId);
      }
      
      logger.info(`Cleaned up ${agentsToRemove.length} idle agents`, {
        remainingAgents: this.agents.size
      });
    }
  }

  public getStats(): { totalAgents: number; totalSessions: number; agentsByType: Record<string, number> } {
    const agentsByType: Record<string, number> = {};
    
    for (const agent of this.agents.values()) {
      agentsByType[agent.type] = (agentsByType[agent.type] || 0) + 1;
    }

    return {
      totalAgents: this.agents.size,
      totalSessions: this.sessionAgents.size,
      agentsByType
    };
  }
}
