import { Tool } from "@langchain/core/tools";
import { z } from "zod";
import axios from "axios";
import { config } from "../config/environment";
import { logger } from "../utils/logger";

const webSearchSchema = z.object({
  query: z.string().describe("The search query to execute"),
  numResults: z.number().optional().default(5).describe("Number of results to return (1-10)"),
});

export class WebSearchTool extends Tool {
  name = "web_search";
  description = "Search the web for current information. Use this when you need up-to-date information or facts that might not be in your training data.";
  schema = webSearchSchema;

  constructor() {
    super();
  }

  async _call(input: z.infer<typeof webSearchSchema>): Promise<string> {
    try {
      const { query, numResults } = input;
      
      logger.info("Web search tool called", { query, numResults });

      // Use SerpAPI for web search if available
      if (config.serp<PERSON><PERSON><PERSON><PERSON>) {
        return await this.searchWithSerpAPI(query, numResults);
      }
      
      // Fallback to a simple search simulation
      return await this.simulateSearch(query, numResults);

    } catch (error) {
      logger.error("Web search tool error", {
        error: error instanceof Error ? error.message : error,
        input
      });
      
      return `Error performing web search: ${error instanceof Error ? error.message : "Unknown error"}`;
    }
  }

  private async searchWithSerpAPI(query: string, numResults: number): Promise<string> {
    try {
      const response = await axios.get("https://serpapi.com/search", {
        params: {
          q: query,
          api_key: config.serpApiKey,
          engine: "google",
          num: numResults,
          format: "json"
        },
        timeout: 10000
      });

      const results = response.data.organic_results || [];
      
      if (results.length === 0) {
        return `No search results found for query: "${query}"`;
      }

      const formattedResults = results.slice(0, numResults).map((result: any, index: number) => {
        return `${index + 1}. **${result.title}**
   URL: ${result.link}
   Snippet: ${result.snippet || "No description available"}`;
      }).join("\n\n");

      return `Web search results for "${query}":\n\n${formattedResults}`;

    } catch (error) {
      logger.error("SerpAPI search error", {
        error: error instanceof Error ? error.message : error,
        query
      });
      
      throw new Error(`SerpAPI search failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  private async simulateSearch(query: string, numResults: number): Promise<string> {
    // Simulate search results when no API key is available
    const simulatedResults = [
      {
        title: `Search Result 1 for "${query}"`,
        url: "https://example.com/result1",
        snippet: "This is a simulated search result. In a real implementation, this would contain actual web search results."
      },
      {
        title: `Search Result 2 for "${query}"`,
        url: "https://example.com/result2", 
        snippet: "Another simulated result. Configure SERP_API_KEY environment variable to enable real web search."
      },
      {
        title: `Search Result 3 for "${query}"`,
        url: "https://example.com/result3",
        snippet: "Third simulated result showing what web search results would look like."
      }
    ];

    const formattedResults = simulatedResults.slice(0, numResults).map((result, index) => {
      return `${index + 1}. **${result.title}**
   URL: ${result.url}
   Snippet: ${result.snippet}`;
    }).join("\n\n");

    return `Simulated web search results for "${query}":\n\n${formattedResults}\n\n*Note: These are simulated results. Configure SERP_API_KEY for real web search.*`;
  }
}
