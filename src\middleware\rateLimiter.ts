import { Request, Response, NextFunction } from "express";
import { RateLimiterMemory, RateLimiterRedis } from "rate-limiter-flexible";
import { config } from "../config/environment";
import { logger } from "../utils/logger";
import { RateLimitError } from "./errorHandler";

// Rate limiter configuration
const rateLimiterConfig = {
  keyPrefix: "langchain_agent_rl",
  points: config.rateLimitMaxRequests, // Number of requests
  duration: Math.floor(config.rateLimitWindowMs / 1000), // Per duration in seconds
  blockDuration: 60, // Block for 60 seconds if limit exceeded
};

// Create rate limiter instance
const rateLimiter = new RateLimiterMemory(rateLimiterConfig);

// Different rate limits for different endpoints
const endpointLimits = {
  "/api/agents/chat": {
    points: 30, // 30 requests
    duration: 60, // per minute
    blockDuration: 60
  },
  "/api/agents/plan-execute": {
    points: 10, // 10 requests
    duration: 60, // per minute
    blockDuration: 120
  },
  "/api/agents/react": {
    points: 20, // 20 requests
    duration: 60, // per minute
    blockDuration: 60
  },
  "/api/agents/tool": {
    points: 50, // 50 requests
    duration: 60, // per minute
    blockDuration: 30
  }
};

// Create specific rate limiters for different endpoints
const endpointRateLimiters = new Map<string, RateLimiterMemory>();

for (const [endpoint, config] of Object.entries(endpointLimits)) {
  endpointRateLimiters.set(endpoint, new RateLimiterMemory({
    keyPrefix: `langchain_agent_rl_${endpoint.replace(/[^a-zA-Z0-9]/g, "_")}`,
    ...config
  }));
}

// Get client identifier for rate limiting
const getClientId = (req: Request): string => {
  // Try to get user ID from auth context
  const userId = (req as any).user?.id;
  if (userId) {
    return `user_${userId}`;
  }

  // Fall back to IP address
  const forwarded = req.headers["x-forwarded-for"];
  const ip = forwarded ? (forwarded as string).split(",")[0] : req.connection.remoteAddress;
  return `ip_${ip}`;
};

// Main rate limiter middleware
export const rateLimiter = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const clientId = getClientId(req);
    const endpoint = req.path;
    
    // Get appropriate rate limiter
    let limiter = endpointRateLimiters.get(endpoint);
    if (!limiter) {
      // Use default rate limiter for unspecified endpoints
      limiter = rateLimiter;
    }

    // Check rate limit
    const resRateLimiter = await limiter.consume(clientId);
    
    // Add rate limit headers
    res.set({
      "X-RateLimit-Limit": limiter.points.toString(),
      "X-RateLimit-Remaining": resRateLimiter.remainingPoints?.toString() || "0",
      "X-RateLimit-Reset": new Date(Date.now() + resRateLimiter.msBeforeNext).toISOString()
    });

    next();
  } catch (rejRes: any) {
    // Rate limit exceeded
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    
    res.set({
      "X-RateLimit-Limit": rejRes.totalHits?.toString() || "0",
      "X-RateLimit-Remaining": "0",
      "X-RateLimit-Reset": new Date(Date.now() + rejRes.msBeforeNext).toISOString(),
      "Retry-After": secs.toString()
    });

    logger.warn("Rate limit exceeded", {
      clientId: getClientId(req),
      endpoint: req.path,
      method: req.method,
      retryAfter: secs
    });

    const error = new RateLimitError(`Rate limit exceeded. Try again in ${secs} seconds.`);
    next(error);
  }
};

// Specific rate limiter for heavy operations
export const heavyOperationLimiter = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  const heavyLimiter = new RateLimiterMemory({
    keyPrefix: "langchain_agent_heavy",
    points: 5, // 5 requests
    duration: 300, // per 5 minutes
    blockDuration: 600 // block for 10 minutes
  });

  try {
    const clientId = getClientId(req);
    const resRateLimiter = await heavyLimiter.consume(clientId);
    
    res.set({
      "X-RateLimit-Heavy-Limit": "5",
      "X-RateLimit-Heavy-Remaining": resRateLimiter.remainingPoints?.toString() || "0",
      "X-RateLimit-Heavy-Reset": new Date(Date.now() + resRateLimiter.msBeforeNext).toISOString()
    });

    next();
  } catch (rejRes: any) {
    const secs = Math.round(rejRes.msBeforeNext / 1000) || 1;
    
    res.set({
      "X-RateLimit-Heavy-Limit": "5",
      "X-RateLimit-Heavy-Remaining": "0",
      "X-RateLimit-Heavy-Reset": new Date(Date.now() + rejRes.msBeforeNext).toISOString(),
      "Retry-After": secs.toString()
    });

    logger.warn("Heavy operation rate limit exceeded", {
      clientId: getClientId(req),
      endpoint: req.path,
      method: req.method,
      retryAfter: secs
    });

    const error = new RateLimitError(`Heavy operation rate limit exceeded. Try again in ${Math.ceil(secs / 60)} minutes.`);
    next(error);
  }
};
