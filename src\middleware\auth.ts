import { Request, Response, NextFunction } from "express";
import * as admin from "firebase-admin";
import { AuthenticationError, AuthorizationError } from "./errorHandler";
import { logger } from "../utils/logger";

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email?: string;
        role?: string;
        permissions?: string[];
        metadata?: Record<string, any>;
      };
    }
  }
}

// User roles and permissions
export enum UserRole {
  ADMIN = "admin",
  USER = "user",
  DEVELOPER = "developer",
  GUEST = "guest"
}

export enum Permission {
  AGENT_CREATE = "agent:create",
  AGENT_EXECUTE = "agent:execute",
  AGENT_DELETE = "agent:delete",
  AGENT_VIEW = "agent:view",
  MEMORY_READ = "memory:read",
  MEMORY_WRITE = "memory:write",
  TOOLS_USE = "tools:use",
  ADMIN_ACCESS = "admin:access"
}

// Role-based permissions mapping
const rolePermissions: Record<UserRole, Permission[]> = {
  [UserRole.ADMIN]: [
    Permission.AGENT_CREATE,
    Permission.AGENT_EXECUTE,
    Permission.AGENT_DELETE,
    Permission.AGENT_VIEW,
    Permission.MEMORY_READ,
    Permission.MEMORY_WRITE,
    Permission.TOOLS_USE,
    Permission.ADMIN_ACCESS
  ],
  [UserRole.DEVELOPER]: [
    Permission.AGENT_CREATE,
    Permission.AGENT_EXECUTE,
    Permission.AGENT_VIEW,
    Permission.MEMORY_READ,
    Permission.MEMORY_WRITE,
    Permission.TOOLS_USE
  ],
  [UserRole.USER]: [
    Permission.AGENT_EXECUTE,
    Permission.AGENT_VIEW,
    Permission.MEMORY_READ,
    Permission.TOOLS_USE
  ],
  [UserRole.GUEST]: [
    Permission.AGENT_VIEW
  ]
};

// Authentication middleware
export const authMiddleware = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    // Skip auth for health checks
    if (req.path.startsWith("/health")) {
      return next();
    }

    // Get token from Authorization header
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      throw new AuthenticationError("Missing or invalid authorization header");
    }

    const token = authHeader.substring(7);
    
    try {
      // Verify Firebase token
      const decodedToken = await admin.auth().verifyIdToken(token);
      
      // Get user role from custom claims or default to USER
      const role = (decodedToken.role as UserRole) || UserRole.USER;
      const permissions = rolePermissions[role] || [];

      // Set user context
      req.user = {
        id: decodedToken.uid,
        email: decodedToken.email,
        role,
        permissions,
        metadata: {
          firebase_claims: decodedToken
        }
      };

      logger.info("User authenticated", {
        userId: req.user.id,
        email: req.user.email,
        role: req.user.role,
        path: req.path,
        method: req.method
      });

      next();
    } catch (firebaseError) {
      logger.error("Firebase token verification failed", {
        error: firebaseError instanceof Error ? firebaseError.message : firebaseError,
        path: req.path,
        method: req.method
      });
      
      throw new AuthenticationError("Invalid or expired token");
    }
  } catch (error) {
    next(error);
  }
};

// Authorization middleware factory
export const requirePermission = (permission: Permission) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new AuthenticationError("Authentication required"));
    }

    if (!req.user.permissions?.includes(permission)) {
      logger.warn("Permission denied", {
        userId: req.user.id,
        requiredPermission: permission,
        userPermissions: req.user.permissions,
        path: req.path,
        method: req.method
      });
      
      return next(new AuthorizationError(`Permission ${permission} required`));
    }

    next();
  };
};

// Role-based authorization middleware
export const requireRole = (role: UserRole) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    if (!req.user) {
      return next(new AuthenticationError("Authentication required"));
    }

    if (req.user.role !== role) {
      logger.warn("Role access denied", {
        userId: req.user.id,
        requiredRole: role,
        userRole: req.user.role,
        path: req.path,
        method: req.method
      });
      
      return next(new AuthorizationError(`Role ${role} required`));
    }

    next();
  };
};

// Optional authentication middleware (doesn't fail if no token)
export const optionalAuth = async (req: Request, res: Response, next: NextFunction): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith("Bearer ")) {
      const token = authHeader.substring(7);
      
      try {
        const decodedToken = await admin.auth().verifyIdToken(token);
        const role = (decodedToken.role as UserRole) || UserRole.USER;
        const permissions = rolePermissions[role] || [];

        req.user = {
          id: decodedToken.uid,
          email: decodedToken.email,
          role,
          permissions,
          metadata: {
            firebase_claims: decodedToken
          }
        };
      } catch (firebaseError) {
        // Log but don't fail
        logger.debug("Optional auth token verification failed", {
          error: firebaseError instanceof Error ? firebaseError.message : firebaseError
        });
      }
    }
    
    next();
  } catch (error) {
    // Don't fail on optional auth errors
    next();
  }
};
