import { BaseMessage, HumanMessage, AIMessage } from "@langchain/core/messages";
import { IMemoryManager, IMemoryStorage, ConversationState, MemoryConfig, MemoryType } from "../types/memory";
import { FirestoreMemoryStorage } from "./FirestoreMemoryStorage";
import { logger, logMemoryOperation } from "../utils/logger";

export class MemoryManager implements IMemoryManager {
  private static instance: MemoryManager;
  private storage: IMemoryStorage;
  private config: MemoryConfig;

  private constructor(config: MemoryConfig = { type: MemoryType.BUFFER }) {
    this.config = config;
    this.storage = new FirestoreMemoryStorage();
  }

  public static getInstance(config?: MemoryConfig): MemoryManager {
    if (!MemoryManager.instance) {
      MemoryManager.instance = new MemoryManager(config);
    }
    return MemoryManager.instance;
  }

  async saveContext(sessionId: string, input: string, output: string): Promise<void> {
    try {
      logMemoryOperation("saveContext", sessionId, {
        inputLength: input.length,
        outputLength: output.length
      });

      // Load existing state or create new one
      let state = await this.storage.load(sessionId);
      
      if (!state) {
        state = {
          sessionId,
          messages: [],
          createdAt: new Date(),
          updatedAt: new Date()
        };
      }

      // Add new messages
      state.messages.push(
        new HumanMessage(input),
        new AIMessage(output)
      );

      // Apply memory management based on configuration
      state = await this.applyMemoryManagement(state);
      
      state.updatedAt = new Date();

      // Save updated state
      await this.storage.save(sessionId, state);

      logger.info("Context saved to memory", {
        sessionId,
        totalMessages: state.messages.length
      });

    } catch (error) {
      logger.error("Failed to save context", {
        sessionId,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  async loadMemoryVariables(sessionId: string): Promise<Record<string, any>> {
    try {
      logMemoryOperation("loadMemoryVariables", sessionId, {});

      const state = await this.storage.load(sessionId);
      
      if (!state) {
        return {
          history: "",
          chat_history: []
        };
      }

      // Format messages for different memory types
      const variables: Record<string, any> = {
        chat_history: state.messages
      };

      // Add formatted history string
      variables.history = this.formatMessagesAsString(state.messages);

      // Add summary if available
      if (state.summary) {
        variables.summary = state.summary;
      }

      // Add entities if available
      if (state.entities && Object.keys(state.entities).length > 0) {
        variables.entities = state.entities;
      }

      logger.info("Memory variables loaded", {
        sessionId,
        messageCount: state.messages.length,
        hasSummary: !!state.summary,
        entityCount: Object.keys(state.entities || {}).length
      });

      return variables;

    } catch (error) {
      logger.error("Failed to load memory variables", {
        sessionId,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  async getMessages(sessionId: string): Promise<BaseMessage[]> {
    try {
      const state = await this.storage.load(sessionId);
      return state?.messages || [];
    } catch (error) {
      logger.error("Failed to get messages", {
        sessionId,
        error: error instanceof Error ? error.message : error
      });
      return [];
    }
  }

  async clearSession(sessionId: string): Promise<void> {
    try {
      logMemoryOperation("clearSession", sessionId, {});
      await this.storage.delete(sessionId);
      
      logger.info("Session cleared", { sessionId });
    } catch (error) {
      logger.error("Failed to clear session", {
        sessionId,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  async clearAll(): Promise<void> {
    try {
      logMemoryOperation("clearAll", "system", {});
      
      // Get all sessions and delete them
      const sessionIds = await this.storage.list();
      
      for (const sessionId of sessionIds) {
        await this.storage.delete(sessionId);
      }

      logger.info("All sessions cleared", { count: sessionIds.length });
    } catch (error) {
      logger.error("Failed to clear all sessions", {
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  private async applyMemoryManagement(state: ConversationState): Promise<ConversationState> {
    switch (this.config.type) {
      case MemoryType.BUFFER_WINDOW:
        return this.applyBufferWindow(state);
      
      case MemoryType.SUMMARY:
        return await this.applySummaryManagement(state);
      
      case MemoryType.CONVERSATION_SUMMARY_BUFFER:
        return await this.applyConversationSummaryBuffer(state);
      
      default:
        return this.applyBufferManagement(state);
    }
  }

  private applyBufferManagement(state: ConversationState): ConversationState {
    // Simple buffer - keep all messages up to token limit
    if (this.config.maxTokens) {
      const estimatedTokens = this.estimateTokenCount(state.messages);
      if (estimatedTokens > this.config.maxTokens) {
        // Remove oldest messages until under limit
        while (state.messages.length > 2 && this.estimateTokenCount(state.messages) > this.config.maxTokens) {
          state.messages.splice(0, 2); // Remove human-AI pair
        }
      }
    }

    if (this.config.maxMessages && state.messages.length > this.config.maxMessages) {
      const excess = state.messages.length - this.config.maxMessages;
      state.messages.splice(0, excess);
    }

    return state;
  }

  private applyBufferWindow(state: ConversationState): ConversationState {
    const maxMessages = this.config.maxMessages || 20;
    
    if (state.messages.length > maxMessages) {
      // Keep only the most recent messages
      state.messages = state.messages.slice(-maxMessages);
    }

    return state;
  }

  private async applySummaryManagement(state: ConversationState): Promise<ConversationState> {
    // For summary memory, we would typically use an LLM to create summaries
    // This is a simplified implementation
    if (state.messages.length > 10) {
      const oldMessages = state.messages.slice(0, -4); // Keep last 4 messages
      const summary = this.createSimpleSummary(oldMessages);
      
      state.summary = state.summary ? `${state.summary}\n\n${summary}` : summary;
      state.messages = state.messages.slice(-4);
    }

    return state;
  }

  private async applyConversationSummaryBuffer(state: ConversationState): Promise<ConversationState> {
    const maxMessages = this.config.maxMessages || 10;
    
    if (state.messages.length > maxMessages) {
      const messagesToSummarize = state.messages.slice(0, -maxMessages);
      const summary = this.createSimpleSummary(messagesToSummarize);
      
      state.summary = state.summary ? `${state.summary}\n\n${summary}` : summary;
      state.messages = state.messages.slice(-maxMessages);
    }

    return state;
  }

  private estimateTokenCount(messages: BaseMessage[]): number {
    // Rough estimation: 1 token ≈ 4 characters
    const totalChars = messages.reduce((sum, msg) => {
      return sum + (typeof msg.content === 'string' ? msg.content.length : 0);
    }, 0);
    
    return Math.ceil(totalChars / 4);
  }

  private createSimpleSummary(messages: BaseMessage[]): string {
    // Simple summary creation - in production, use an LLM
    const messageCount = messages.length;
    const topics = this.extractTopics(messages);
    
    return `Previous conversation (${messageCount} messages) covered topics: ${topics.join(", ")}`;
  }

  private extractTopics(messages: BaseMessage[]): string[] {
    // Simple topic extraction - in production, use NLP techniques
    const allText = messages
      .map(msg => typeof msg.content === 'string' ? msg.content : '')
      .join(' ')
      .toLowerCase();
    
    const commonTopics = ['weather', 'calculation', 'search', 'help', 'question', 'information'];
    return commonTopics.filter(topic => allText.includes(topic));
  }

  private formatMessagesAsString(messages: BaseMessage[]): string {
    return messages
      .map(msg => {
        const type = msg._getType();
        const content = typeof msg.content === 'string' ? msg.content : '';
        return `${type === 'human' ? 'Human' : 'AI'}: ${content}`;
      })
      .join('\n');
  }

  public async getStats(): Promise<{ totalSessions: number; totalMessages: number }> {
    try {
      if ('getStats' in this.storage) {
        return await (this.storage as any).getStats();
      }
      
      // Fallback implementation
      const sessionIds = await this.storage.list();
      let totalMessages = 0;
      
      for (const sessionId of sessionIds) {
        const messages = await this.getMessages(sessionId);
        totalMessages += messages.length;
      }
      
      return {
        totalSessions: sessionIds.length,
        totalMessages
      };
    } catch (error) {
      logger.error("Failed to get memory stats", {
        error: error instanceof Error ? error.message : error
      });
      return { totalSessions: 0, totalMessages: 0 };
    }
  }
}
