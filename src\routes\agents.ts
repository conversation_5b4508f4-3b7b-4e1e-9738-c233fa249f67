import { Router, Request, Response } from "express";
import <PERSON><PERSON> from "joi";
import { AgentFactory } from "../core/AgentFactory";
import { AgentRegistry } from "../core/AgentRegistry";
import { AgentType } from "../types/agent";
import { requirePermission, Permission } from "../middleware/auth";
import { asyncHandler, ValidationError } from "../middleware/errorHandler";
import { heavyOperationLimiter } from "../middleware/rateLimiter";
import { logger } from "../utils/logger";

const router = Router();

// Validation schemas
const agentExecutionSchema = Joi.object({
  message: Joi.string().required().min(1).max(10000),
  agentType: Joi.string().valid(...Object.values(AgentType)).required(),
  config: Joi.object({
    model: Joi.string().default("gpt-4"),
    temperature: Joi.number().min(0).max(2).default(0.7),
    maxIterations: Joi.number().min(1).max(20).default(10),
    timeoutMs: Joi.number().min(5000).max(300000).default(30000),
    systemPrompt: Joi.string().max(5000),
    verbose: Joi.boolean().default(false)
  }).default({}),
  context: Joi.object({
    sessionId: Joi.string(),
    conversationId: Joi.string(),
    metadata: Joi.object()
  }).default({})
});

const agentConfigSchema = Joi.object({
  type: Joi.string().valid(...Object.values(AgentType)).required(),
  model: Joi.string().required(),
  temperature: Joi.number().min(0).max(2).default(0.7),
  maxIterations: Joi.number().min(1).max(20).default(10),
  timeoutMs: Joi.number().min(5000).max(300000).default(30000),
  systemPrompt: Joi.string().max(5000),
  verbose: Joi.boolean().default(false),
  metadata: Joi.object()
});

// Execute agent endpoint
router.post("/execute", 
  requirePermission(Permission.AGENT_EXECUTE),
  heavyOperationLimiter,
  asyncHandler(async (req: Request, res: Response) => {
    // Validate input
    const { error, value } = agentExecutionSchema.validate(req.body);
    if (error) {
      throw new ValidationError("Invalid request data", error.details);
    }

    const { message, agentType, config, context } = value;
    
    // Create agent configuration
    const agentConfig = {
      type: agentType,
      ...config
    };

    logger.info("Agent execution requested", {
      userId: req.user?.id,
      agentType,
      messageLength: message.length,
      config: agentConfig
    });

    try {
      // Create agent
      const factory = AgentFactory.getInstance();
      const agent = await factory.createAgent(agentConfig);
      
      // Register agent
      const registry = AgentRegistry.getInstance();
      registry.register(agent);
      
      // Associate with session if provided
      if (context.sessionId) {
        registry.associateWithSession(agent.id, context.sessionId);
      }

      // Execute agent
      const result = await agent.execute({
        message,
        context: {
          ...context,
          userId: req.user?.id
        }
      });

      // Unregister agent after execution
      registry.unregister(agent.id);

      logger.info("Agent execution completed", {
        userId: req.user?.id,
        agentId: agent.id,
        success: result.result.success,
        executionTime: result.result.executionTime,
        tokensUsed: result.result.tokensUsed
      });

      res.json({
        success: true,
        data: {
          agentId: agent.id,
          response: result.response,
          executionResult: result.result,
          context: result.context
        }
      });

    } catch (error) {
      logger.error("Agent execution failed", {
        userId: req.user?.id,
        agentType,
        error: error instanceof Error ? error.message : error
      });

      throw error;
    }
  })
);

// Create persistent agent
router.post("/create",
  requirePermission(Permission.AGENT_CREATE),
  asyncHandler(async (req: Request, res: Response) => {
    // Validate input
    const { error, value } = agentConfigSchema.validate(req.body);
    if (error) {
      throw new ValidationError("Invalid agent configuration", error.details);
    }

    logger.info("Creating persistent agent", {
      userId: req.user?.id,
      config: value
    });

    try {
      // Create agent
      const factory = AgentFactory.getInstance();
      const agent = await factory.createAgent(value);
      
      // Register agent
      const registry = AgentRegistry.getInstance();
      registry.register(agent);

      logger.info("Persistent agent created", {
        userId: req.user?.id,
        agentId: agent.id,
        type: agent.type
      });

      res.json({
        success: true,
        data: {
          agentId: agent.id,
          type: agent.type,
          config: agent.config,
          status: agent.getStatus()
        }
      });

    } catch (error) {
      logger.error("Failed to create persistent agent", {
        userId: req.user?.id,
        config: value,
        error: error instanceof Error ? error.message : error
      });

      throw error;
    }
  })
);

// Get agent status
router.get("/:agentId/status",
  requirePermission(Permission.AGENT_VIEW),
  asyncHandler(async (req: Request, res: Response) => {
    const { agentId } = req.params;
    
    const registry = AgentRegistry.getInstance();
    const agent = registry.get(agentId);
    
    if (!agent) {
      return res.status(404).json({
        success: false,
        error: {
          code: "AGENT_NOT_FOUND",
          message: "Agent not found"
        }
      });
    }

    res.json({
      success: true,
      data: {
        agentId: agent.id,
        type: agent.type,
        status: agent.getStatus(),
        config: agent.config
      }
    });
  })
);

// Stop agent
router.post("/:agentId/stop",
  requirePermission(Permission.AGENT_EXECUTE),
  asyncHandler(async (req: Request, res: Response) => {
    const { agentId } = req.params;
    
    const registry = AgentRegistry.getInstance();
    const agent = registry.get(agentId);
    
    if (!agent) {
      return res.status(404).json({
        success: false,
        error: {
          code: "AGENT_NOT_FOUND",
          message: "Agent not found"
        }
      });
    }

    await agent.stop();

    logger.info("Agent stopped", {
      userId: req.user?.id,
      agentId: agent.id
    });

    res.json({
      success: true,
      data: {
        agentId: agent.id,
        status: agent.getStatus()
      }
    });
  })
);

// Delete agent
router.delete("/:agentId",
  requirePermission(Permission.AGENT_DELETE),
  asyncHandler(async (req: Request, res: Response) => {
    const { agentId } = req.params;
    
    const registry = AgentRegistry.getInstance();
    const agent = registry.get(agentId);
    
    if (!agent) {
      return res.status(404).json({
        success: false,
        error: {
          code: "AGENT_NOT_FOUND",
          message: "Agent not found"
        }
      });
    }

    registry.unregister(agentId);

    logger.info("Agent deleted", {
      userId: req.user?.id,
      agentId
    });

    res.json({
      success: true,
      data: {
        agentId,
        message: "Agent deleted successfully"
      }
    });
  })
);

// List agents
router.get("/",
  requirePermission(Permission.AGENT_VIEW),
  asyncHandler(async (req: Request, res: Response) => {
    const registry = AgentRegistry.getInstance();
    const stats = registry.getStats();

    res.json({
      success: true,
      data: {
        stats,
        supportedTypes: Object.values(AgentType)
      }
    });
  })
);

export { router as agentRoutes };
