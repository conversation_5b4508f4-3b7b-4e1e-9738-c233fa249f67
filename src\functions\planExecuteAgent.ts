import * as functions from "firebase-functions";
import { PlanExecuteAgent } from "../agents/PlanExecuteAgent";
import { AgentType } from "../types/agent";
import { logger } from "../utils/logger";
import { validateConfig } from "../config/environment";

// Validate environment on cold start
validateConfig();

export const planExecuteAgent = functions
  .region("us-central1")
  .runWith({
    timeoutSeconds: 540, // 9 minutes for complex planning tasks
    memory: "2GB",
  })
  .https
  .onCall(async (data, context) => {
    try {
      // Validate authentication
      if (!context.auth) {
        throw new functions.https.HttpsError(
          "unauthenticated",
          "Authentication required"
        );
      }

      // Validate input
      if (!data.message || typeof data.message !== "string") {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Message is required and must be a string"
        );
      }

      const { message, config: agentConfig = {}, context: agentContext = {} } = data;

      logger.info("Plan-Execute agent function called", {
        userId: context.auth.uid,
        messageLength: message.length,
        config: agentConfig
      });

      // Create agent configuration
      const config = {
        type: AgentType.PLAN_EXECUTE,
        model: agentConfig.model || "gpt-4",
        temperature: agentConfig.temperature || 0.7,
        maxIterations: agentConfig.maxIterations || 15, // Higher for planning
        timeoutMs: agentConfig.timeoutMs || 480000, // 8 minutes
        systemPrompt: agentConfig.systemPrompt,
        verbose: agentConfig.verbose || false,
        tools: agentConfig.tools || [],
        ...agentConfig
      };

      // Create and execute agent
      const agent = new PlanExecuteAgent(config);
      
      const result = await agent.execute({
        message,
        context: {
          sessionId: agentContext.sessionId || `plan_${context.auth.uid}_${Date.now()}`,
          userId: context.auth.uid,
          conversationId: agentContext.conversationId,
          metadata: agentContext.metadata
        }
      });

      logger.info("Plan-Execute agent execution completed", {
        userId: context.auth.uid,
        agentId: agent.id,
        success: result.result.success,
        executionTime: result.result.executionTime,
        steps: result.result.steps?.length || 0
      });

      return {
        success: true,
        data: {
          response: result.response,
          executionResult: result.result,
          plan: result.result.metadata?.plan,
          steps: result.result.steps
        }
      };

    } catch (error) {
      logger.error("Plan-Execute agent function error", {
        userId: context.auth?.uid,
        error: error instanceof Error ? error.message : error
      });

      if (error instanceof functions.https.HttpsError) {
        throw error;
      }

      throw new functions.https.HttpsError(
        "internal",
        "An error occurred while processing your request"
      );
    }
  });
