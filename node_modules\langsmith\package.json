{"name": "langsmith", "version": "0.3.47", "description": "Client library to connect to the LangSmith LLM Tracing and Evaluation Platform.", "packageManager": "yarn@1.22.19", "files": ["dist/", "client.cjs", "client.js", "client.d.ts", "client.d.cts", "run_trees.cjs", "run_trees.js", "run_trees.d.ts", "run_trees.d.cts", "traceable.cjs", "traceable.js", "traceable.d.ts", "traceable.d.cts", "evaluation.cjs", "evaluation.js", "evaluation.d.ts", "evaluation.d.cts", "evaluation/langchain.cjs", "evaluation/langchain.js", "evaluation/langchain.d.ts", "evaluation/langchain.d.cts", "schemas.cjs", "schemas.js", "schemas.d.ts", "schemas.d.cts", "langchain.cjs", "langchain.js", "langchain.d.ts", "langchain.d.cts", "jest.cjs", "jest.js", "jest.d.ts", "jest.d.cts", "jest/reporter.cjs", "jest/reporter.js", "jest/reporter.d.ts", "jest/reporter.d.cts", "vercel.cjs", "vercel.js", "vercel.d.ts", "vercel.d.cts", "vitest.cjs", "vitest.js", "vitest.d.ts", "vitest.d.cts", "vitest/reporter.cjs", "vitest/reporter.js", "vitest/reporter.d.ts", "vitest/reporter.d.cts", "wrappers.cjs", "wrappers.js", "wrappers.d.ts", "wrappers.d.cts", "anonymizer.cjs", "anonymizer.js", "anonymizer.d.ts", "anonymizer.d.cts", "wrappers/openai.cjs", "wrappers/openai.js", "wrappers/openai.d.ts", "wrappers/openai.d.cts", "wrappers/vercel.cjs", "wrappers/vercel.js", "wrappers/vercel.d.ts", "wrappers/vercel.d.cts", "singletons/traceable.cjs", "singletons/traceable.js", "singletons/traceable.d.ts", "singletons/traceable.d.cts", "utils/jestlike.cjs", "utils/jestlike.js", "utils/jestlike.d.ts", "utils/jestlike.d.cts", "experimental/otel/setup.cjs", "experimental/otel/setup.js", "experimental/otel/setup.d.ts", "experimental/otel/setup.d.cts", "experimental/otel/exporter.cjs", "experimental/otel/exporter.js", "experimental/otel/exporter.d.ts", "experimental/otel/exporter.d.cts", "index.cjs", "index.js", "index.d.ts", "index.d.cts"], "type": "module", "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "yarn clean && yarn build:esm && yarn build:cjs && node scripts/create-entrypoints.js", "build:typedoc": "yarn build && rm -rf ./_build/api_refs && npx typedoc", "bump-version": "node scripts/bump-version.js", "check-version": "node scripts/check-version.js", "check-npm-version": "node scripts/check-npm-version.js", "clean": "rm -rf dist/ && node scripts/create-entrypoints.js clean", "build:esm": "rm -f src/package.json && tsc --outDir dist/ && rm -rf dist/tests dist/**/tests", "build:cjs": "echo '{}' > src/package.json && tsc --outDir dist-cjs/ -p tsconfig.cjs.json && node scripts/move-cjs-to-dist.js && rm -r dist-cjs src/package.json", "test": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --passWithNoTests --testPathIgnorePatterns='\\.int\\.test.[tj]s' --testTimeout 30000", "test:integration": "cross-env NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.int\\.test.ts --testTimeout 100000", "test:single": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.cjs --testTimeout 100000", "watch:single": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --watch --config jest.config.cjs --testTimeout 100000", "test:eval:vitest": "vitest run --config ls.vitest.config.ts", "lint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint:fix": "yarn lint --fix", "format": "prettier --write 'src/**/*.{ts,tsx}'", "format:check": "prettier --check 'src/**/*.{ts,tsx}'", "precommit": "lint-staged", "prepublish": "yarn run build"}, "repository": {"type": "git", "url": "git+https://github.com/langchain-ai/langsmith-sdk.git"}, "keywords": ["LLM", "Tracing", "Evaluation", "<PERSON><PERSON><PERSON><PERSON>"], "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/langchain-ai/langsmith-sdk/issues"}, "homepage": "https://github.com/langchain-ai/langsmith-sdk#readme", "dependencies": {"@types/uuid": "^10.0.0", "chalk": "^4.1.2", "console-table-printer": "^2.12.1", "p-queue": "^6.6.2", "p-retry": "4", "semver": "^7.6.3", "uuid": "^10.0.0"}, "devDependencies": {"@ai-sdk/anthropic": "2.0.0-beta.8", "@ai-sdk/openai": "2.0.0-beta.11", "@babel/preset-env": "^7.22.4", "@faker-js/faker": "^8.4.1", "@jest/globals": "^29.5.0", "@jest/reporters": "^29.7.0", "@langchain/core": "^0.3.61", "@langchain/langgraph": "^0.3.6", "@langchain/openai": "^0.5.16", "@opentelemetry/api": "^1.9.0", "@opentelemetry/auto-instrumentations-node": "^0.58.0", "@opentelemetry/sdk-node": "^0.200.0", "@opentelemetry/sdk-trace-base": "^2.0.0", "@opentelemetry/sdk-trace-node": "^2.0.0", "@tsconfig/recommended": "^1.0.2", "@types/jest": "^29.5.1", "@types/node-fetch": "^2.6.12", "@typescript-eslint/eslint-plugin": "^5.59.8", "@typescript-eslint/parser": "^5.59.8", "ai": "5.0.0-beta.25", "babel-jest": "^29.5.0", "cross-env": "^7.0.3", "dotenv": "^16.1.3", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "langchain": "^0.3.29", "node-fetch": "^2.7.0", "openai": "^5.8.2", "prettier": "^2.8.8", "ts-jest": "^29.1.0", "ts-node": "^10.9.1", "typedoc": "^0.27.6", "typedoc-plugin-expand-object-like-types": "^0.1.2", "typescript": "^5.4.5", "vitest": "^3.1.3", "zod": "^3.25.76"}, "peerDependencies": {"@opentelemetry/api": "*", "@opentelemetry/exporter-trace-otlp-proto": "*", "@opentelemetry/sdk-trace-base": "*", "openai": "*"}, "peerDependenciesMeta": {"openai": {"optional": true}, "@opentelemetry/api": {"optional": true}, "@opentelemetry/exporter-trace-otlp-proto": {"optional": true}, "@opentelemetry/sdk-trace-base": {"optional": true}}, "lint-staged": {"**/*.{ts,tsx}": ["prettier --write --ignore-unknown", "eslint --cache --fix"]}, "exports": {".": {"types": {"import": "./index.d.ts", "require": "./index.d.cts", "default": "./index.d.ts"}, "import": "./index.js", "require": "./index.cjs"}, "./client": {"types": {"import": "./client.d.ts", "require": "./client.d.cts", "default": "./client.d.ts"}, "import": "./client.js", "require": "./client.cjs"}, "./run_trees": {"types": {"import": "./run_trees.d.ts", "require": "./run_trees.d.cts", "default": "./run_trees.d.ts"}, "import": "./run_trees.js", "require": "./run_trees.cjs"}, "./traceable": {"types": {"import": "./traceable.d.ts", "require": "./traceable.d.cts", "default": "./traceable.d.ts"}, "import": "./traceable.js", "require": "./traceable.cjs"}, "./evaluation": {"types": {"import": "./evaluation.d.ts", "require": "./evaluation.d.cts", "default": "./evaluation.d.ts"}, "import": "./evaluation.js", "require": "./evaluation.cjs"}, "./evaluation/langchain": {"types": {"import": "./evaluation/langchain.d.ts", "require": "./evaluation/langchain.d.cts", "default": "./evaluation/langchain.d.ts"}, "import": "./evaluation/langchain.js", "require": "./evaluation/langchain.cjs"}, "./schemas": {"types": {"import": "./schemas.d.ts", "require": "./schemas.d.cts", "default": "./schemas.d.ts"}, "import": "./schemas.js", "require": "./schemas.cjs"}, "./langchain": {"types": {"import": "./langchain.d.ts", "require": "./langchain.d.cts", "default": "./langchain.d.ts"}, "import": "./langchain.js", "require": "./langchain.cjs"}, "./jest": {"types": {"import": "./jest.d.ts", "require": "./jest.d.cts", "default": "./jest.d.ts"}, "import": "./jest.js", "require": "./jest.cjs"}, "./jest/reporter": {"types": {"import": "./jest/reporter.d.ts", "require": "./jest/reporter.d.cts", "default": "./jest/reporter.d.ts"}, "import": "./jest/reporter.js", "require": "./jest/reporter.cjs"}, "./vercel": {"types": {"import": "./vercel.d.ts", "require": "./vercel.d.cts", "default": "./vercel.d.ts"}, "import": "./vercel.js", "require": "./vercel.cjs"}, "./vitest": {"types": {"import": "./vitest.d.ts", "require": "./vitest.d.cts", "default": "./vitest.d.ts"}, "import": "./vitest.js", "require": "./vitest.cjs"}, "./vitest/reporter": {"types": {"import": "./vitest/reporter.d.ts", "require": "./vitest/reporter.d.cts", "default": "./vitest/reporter.d.ts"}, "import": "./vitest/reporter.js", "require": "./vitest/reporter.cjs"}, "./wrappers": {"types": {"import": "./wrappers.d.ts", "require": "./wrappers.d.cts", "default": "./wrappers.d.ts"}, "import": "./wrappers.js", "require": "./wrappers.cjs"}, "./anonymizer": {"types": {"import": "./anonymizer.d.ts", "require": "./anonymizer.d.cts", "default": "./anonymizer.d.ts"}, "import": "./anonymizer.js", "require": "./anonymizer.cjs"}, "./wrappers/openai": {"types": {"import": "./wrappers/openai.d.ts", "require": "./wrappers/openai.d.cts", "default": "./wrappers/openai.d.ts"}, "import": "./wrappers/openai.js", "require": "./wrappers/openai.cjs"}, "./wrappers/vercel": {"types": {"import": "./wrappers/vercel.d.ts", "require": "./wrappers/vercel.d.cts", "default": "./wrappers/vercel.d.ts"}, "import": "./wrappers/vercel.js", "require": "./wrappers/vercel.cjs"}, "./singletons/traceable": {"types": {"import": "./singletons/traceable.d.ts", "require": "./singletons/traceable.d.cts", "default": "./singletons/traceable.d.ts"}, "import": "./singletons/traceable.js", "require": "./singletons/traceable.cjs"}, "./utils/jestlike": {"types": {"import": "./utils/jestlike.d.ts", "require": "./utils/jestlike.d.cts", "default": "./utils/jestlike.d.ts"}, "import": "./utils/jestlike.js", "require": "./utils/jestlike.cjs"}, "./experimental/otel/setup": {"types": {"import": "./experimental/otel/setup.d.ts", "require": "./experimental/otel/setup.d.cts", "default": "./experimental/otel/setup.d.ts"}, "import": "./experimental/otel/setup.js", "require": "./experimental/otel/setup.cjs"}, "./experimental/otel/exporter": {"types": {"import": "./experimental/otel/exporter.d.ts", "require": "./experimental/otel/exporter.d.cts", "default": "./experimental/otel/exporter.d.ts"}, "import": "./experimental/otel/exporter.js", "require": "./experimental/otel/exporter.cjs"}, "./package.json": "./package.json"}}