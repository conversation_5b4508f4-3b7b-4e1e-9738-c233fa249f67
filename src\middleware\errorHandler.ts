import { Request, Response, NextFunction } from "express";
import { logger } from "../utils/logger";

export interface ApiError extends Error {
  statusCode?: number;
  code?: string;
  details?: any;
}

export class ValidationError extends Error implements ApiError {
  statusCode = 400;
  code = "VALIDATION_ERROR";
  
  constructor(message: string, public details?: any) {
    super(message);
    this.name = "ValidationError";
  }
}

export class AuthenticationError extends Error implements ApiError {
  statusCode = 401;
  code = "AUTHENTICATION_ERROR";
  
  constructor(message: string = "Authentication required") {
    super(message);
    this.name = "AuthenticationError";
  }
}

export class AuthorizationError extends Error implements ApiError {
  statusCode = 403;
  code = "AUTHORIZATION_ERROR";
  
  constructor(message: string = "Insufficient permissions") {
    super(message);
    this.name = "AuthorizationError";
  }
}

export class NotFoundError extends Error implements ApiError {
  statusCode = 404;
  code = "NOT_FOUND";
  
  constructor(message: string = "Resource not found") {
    super(message);
    this.name = "NotFoundError";
  }
}

export class RateLimitError extends Error implements ApiError {
  statusCode = 429;
  code = "RATE_LIMIT_EXCEEDED";
  
  constructor(message: string = "Rate limit exceeded") {
    super(message);
    this.name = "RateLimitError";
  }
}

export class AgentError extends Error implements ApiError {
  statusCode = 500;
  code = "AGENT_ERROR";
  
  constructor(message: string, public details?: any) {
    super(message);
    this.name = "AgentError";
  }
}

export const errorHandler = (
  error: ApiError,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  // Log the error
  logger.error("API Error", {
    error: error.message,
    stack: error.stack,
    statusCode: error.statusCode,
    code: error.code,
    details: error.details,
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get("User-Agent")
  });

  // Determine status code
  const statusCode = error.statusCode || 500;
  
  // Prepare error response
  const errorResponse: any = {
    success: false,
    error: {
      code: error.code || "INTERNAL_ERROR",
      message: error.message || "An unexpected error occurred"
    },
    timestamp: new Date().toISOString(),
    path: req.path
  };

  // Add details in development mode
  if (process.env.NODE_ENV === "development") {
    errorResponse.error.stack = error.stack;
    if (error.details) {
      errorResponse.error.details = error.details;
    }
  }

  // Send error response
  res.status(statusCode).json(errorResponse);
};

// Async error wrapper
export const asyncHandler = (fn: Function) => {
  return (req: Request, res: Response, next: NextFunction) => {
    Promise.resolve(fn(req, res, next)).catch(next);
  };
};

// 404 handler
export const notFoundHandler = (req: Request, res: Response, next: NextFunction): void => {
  const error = new NotFoundError(`Route ${req.method} ${req.path} not found`);
  next(error);
};
