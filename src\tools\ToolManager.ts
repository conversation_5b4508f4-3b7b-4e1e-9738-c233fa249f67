import { Tool } from "@langchain/core/tools";
import { IToolManager, ToolConfig, ToolCategory, ToolExecutionResult } from "../types/tools";
import { WebSearchTool } from "./WebSearchTool";
import { CalculatorTool } from "./CalculatorTool";
import { WeatherTool } from "./WeatherTool";
import { logger } from "../utils/logger";

export class ToolManager implements IToolManager {
  private static instance: ToolManager;
  private tools: Map<string, Tool> = new Map();
  private toolConfigs: Map<string, ToolConfig> = new Map();

  private constructor() {
    this.initializeDefaultTools();
  }

  public static getInstance(): ToolManager {
    if (!ToolManager.instance) {
      ToolManager.instance = new ToolManager();
    }
    return ToolManager.instance;
  }

  private initializeDefaultTools(): void {
    // Register default tools
    this.registerTool(new WebSearchTool(), {
      name: "web_search",
      description: "Search the web for current information",
      category: ToolCategory.WEB_SEARCH,
      enabled: true,
      parameters: {
        requiresApiKey: true,
        apiKeyName: "SERP_API_KEY"
      }
    });

    this.registerTool(new CalculatorTool(), {
      name: "calculator",
      description: "Perform mathematical calculations",
      category: ToolCategory.COMPUTATION,
      enabled: true
    });

    this.registerTool(new WeatherTool(), {
      name: "weather",
      description: "Get current weather information",
      category: ToolCategory.API,
      enabled: true,
      parameters: {
        requiresApiKey: true,
        apiKeyName: "WEATHER_API_KEY"
      }
    });

    logger.info("Default tools initialized", {
      toolCount: this.tools.size,
      tools: Array.from(this.tools.keys())
    });
  }

  public registerTool(tool: Tool, config: ToolConfig): void {
    this.tools.set(config.name, tool);
    this.toolConfigs.set(config.name, config);
    
    logger.info("Tool registered", {
      name: config.name,
      category: config.category,
      enabled: config.enabled
    });
  }

  public unregisterTool(name: string): void {
    const removed = this.tools.delete(name);
    this.toolConfigs.delete(name);
    
    if (removed) {
      logger.info("Tool unregistered", { name });
    }
  }

  public getTool(name: string): Tool | undefined {
    const config = this.toolConfigs.get(name);
    if (config && !config.enabled) {
      return undefined;
    }
    return this.tools.get(name);
  }

  public getTools(category?: ToolCategory): Tool[] {
    const tools: Tool[] = [];
    
    for (const [name, tool] of this.tools.entries()) {
      const config = this.toolConfigs.get(name);
      if (!config || !config.enabled) {
        continue;
      }
      
      if (!category || config.category === category) {
        tools.push(tool);
      }
    }
    
    return tools;
  }

  public getToolConfigs(): ToolConfig[] {
    return Array.from(this.toolConfigs.values());
  }

  public async executeTool(name: string, input: any): Promise<ToolExecutionResult> {
    const startTime = Date.now();
    
    try {
      const tool = this.getTool(name);
      if (!tool) {
        throw new Error(`Tool "${name}" not found or disabled`);
      }

      logger.info("Executing tool", { name, input });

      const result = await tool.call(input);
      const executionTime = Date.now() - startTime;

      logger.info("Tool execution completed", {
        name,
        executionTime,
        success: true
      });

      return {
        success: true,
        result,
        executionTime,
        metadata: {
          toolName: name,
          inputType: typeof input
        }
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : "Unknown error";

      logger.error("Tool execution failed", {
        name,
        error: errorMessage,
        executionTime,
        input
      });

      return {
        success: false,
        error: errorMessage,
        executionTime,
        metadata: {
          toolName: name,
          inputType: typeof input
        }
      };
    }
  }

  public getEnabledToolsForAgent(): Tool[] {
    return this.getTools();
  }

  public enableTool(name: string): void {
    const config = this.toolConfigs.get(name);
    if (config) {
      config.enabled = true;
      logger.info("Tool enabled", { name });
    }
  }

  public disableTool(name: string): void {
    const config = this.toolConfigs.get(name);
    if (config) {
      config.enabled = false;
      logger.info("Tool disabled", { name });
    }
  }

  public getToolsByCategory(): Record<ToolCategory, ToolConfig[]> {
    const result: Record<ToolCategory, ToolConfig[]> = {} as Record<ToolCategory, ToolConfig[]>;
    
    // Initialize all categories
    Object.values(ToolCategory).forEach(category => {
      result[category] = [];
    });

    // Group tools by category
    for (const config of this.toolConfigs.values()) {
      result[config.category].push(config);
    }

    return result;
  }

  public validateToolInput(name: string, input: any): boolean {
    const tool = this.tools.get(name);
    if (!tool) {
      return false;
    }

    try {
      // If the tool has a schema, validate against it
      if ('schema' in tool && tool.schema) {
        const schema = tool.schema as any;
        if (schema.parse) {
          schema.parse(input);
        }
      }
      return true;
    } catch (error) {
      logger.warn("Tool input validation failed", {
        name,
        input,
        error: error instanceof Error ? error.message : error
      });
      return false;
    }
  }
}
