import { Chat<PERSON>penAI } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { PlanAndExecuteAgentExecutor } from "langchain/experimental/plan_and_execute";
import { BaseAgent } from "../core/BaseAgent";
import { 
  AgentConfig, 
  AgentInput, 
  AgentOutput, 
  AgentContext,
  AgentStep,
  AgentType 
} from "../types/agent";
import { config } from "../config/environment";
import { logger } from "../utils/logger";

export class PlanExecuteAgent extends BaseAgent {
  private executor?: PlanAndExecuteAgentExecutor;
  private model: ChatOpenAI | ChatAnthropic;

  constructor(agentConfig: AgentConfig) {
    super({ ...agentConfig, type: AgentType.PLAN_EXECUTE });
    this.initializeModel();
  }

  private initializeModel(): void {
    const modelName = this._config.model.toLowerCase();
    
    if (modelName.includes("gpt") || modelName.includes("openai")) {
      this.model = new ChatOpenAI({
        modelName: this._config.model,
        temperature: this._config.temperature,
        openAIApiKey: config.openaiApiKey,
        maxTokens: 2000,
        timeout: this._config.timeoutMs
      });
    } else if (modelName.includes("claude") || modelName.includes("anthropic")) {
      this.model = new ChatAnthropic({
        modelName: this._config.model,
        temperature: this._config.temperature,
        anthropicApiKey: config.anthropicApiKey,
        maxTokens: 2000,
        timeout: this._config.timeoutMs
      });
    } else {
      throw new Error(`Unsupported model: ${this._config.model}`);
    }
  }

  protected async executeInternal(input: AgentInput, context: AgentContext): Promise<AgentOutput> {
    this.validateInput(input);
    
    const startTime = Date.now();
    const steps: AgentStep[] = [];
    let stepNumber = 1;

    try {
      // Initialize executor if not already done
      if (!this.executor) {
        await this.initializeExecutor();
      }

      logger.info("Starting Plan-and-Execute agent execution", {
        agentId: this.id,
        sessionId: context.sessionId,
        model: this._config.model,
        maxIterations: this._config.maxIterations
      });

      // Add planning step
      steps.push(this.createStep(
        stepNumber++,
        "planning",
        input.message,
        undefined,
        "Creating a step-by-step plan to address the user's request",
        0,
        0
      ));

      // Execute the agent
      const result = await this.executor!.call({
        input: input.message,
        chat_history: input.history || []
      });

      // Add execution steps based on the plan
      if (result.intermediateSteps) {
        for (const [index, step] of result.intermediateSteps.entries()) {
          steps.push(this.createStep(
            stepNumber++,
            "execute_step",
            step.action,
            step.observation,
            `Executing step ${index + 1} of the plan`,
            0,
            0
          ));
        }
      }

      const executionTime = Date.now() - startTime;
      
      // Add final step
      steps.push(this.createStep(
        stepNumber++,
        "finalize",
        undefined,
        result.output,
        "Synthesizing results from all executed steps",
        result.tokensUsed || 0,
        executionTime
      ));

      const executionResult = this.createSuccessResult(
        result.output,
        steps,
        result.tokensUsed || 0,
        executionTime,
        {
          plan: result.plan,
          intermediateSteps: result.intermediateSteps,
          agentType: "plan-execute"
        }
      );

      logger.info("Plan-and-Execute agent execution completed", {
        agentId: this.id,
        sessionId: context.sessionId,
        success: true,
        executionTime,
        tokensUsed: result.tokensUsed,
        planSteps: result.plan?.length || 0
      });

      return {
        response: result.output,
        context,
        result: executionResult
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      
      logger.error("Plan-and-Execute agent execution failed", {
        agentId: this.id,
        sessionId: context.sessionId,
        error: errorMessage,
        executionTime
      });

      throw error;
    }
  }

  private async initializeExecutor(): Promise<void> {
    try {
      // Create the Plan-and-Execute agent executor
      this.executor = PlanAndExecuteAgentExecutor.fromLLMAndTools({
        llm: this.model,
        tools: this._config.tools || [],
        verbose: this._config.verbose,
        maxIterations: this._config.maxIterations
      });

      logger.info("Plan-and-Execute agent executor initialized", {
        agentId: this.id,
        toolCount: this._config.tools?.length || 0,
        maxIterations: this._config.maxIterations
      });

    } catch (error) {
      logger.error("Failed to initialize Plan-and-Execute agent executor", {
        agentId: this.id,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  public updateConfig(newConfig: Partial<AgentConfig>): void {
    super.updateConfig(newConfig);
    
    // Reinitialize model if model-related config changed
    if (newConfig.model || newConfig.temperature) {
      this.initializeModel();
      this.executor = undefined; // Force re-initialization
    }
    
    // Reinitialize executor if tools or iterations changed
    if (newConfig.tools || newConfig.maxIterations) {
      this.executor = undefined; // Force re-initialization
    }
  }
}
