import { Tool } from "@langchain/core/tools";
import { z } from "zod";
import axios from "axios";
import { config } from "../config/environment";
import { logger } from "../utils/logger";

const weatherSchema = z.object({
  location: z.string().describe("The city or location to get weather information for"),
  units: z.enum(["metric", "imperial", "kelvin"]).optional().default("metric").describe("Temperature units (metric=Celsius, imperial=Fahrenheit, kelvin=Kelvin)"),
});

export class WeatherTool extends Tool {
  name = "weather";
  description = "Get current weather information for a specific location. Provides temperature, humidity, wind speed, and weather conditions.";
  schema = weatherSchema;

  constructor() {
    super();
  }

  async _call(input: z.infer<typeof weatherSchema>): Promise<string> {
    try {
      const { location, units } = input;
      
      logger.info("Weather tool called", { location, units });

      // Use OpenWeatherMap API if available
      if (config.weatherApiKey) {
        return await this.getWeatherFromAPI(location, units);
      }
      
      // Fallback to simulated weather data
      return await this.simulateWeather(location, units);

    } catch (error) {
      logger.error("Weather tool error", {
        error: error instanceof Error ? error.message : error,
        input
      });
      
      return `Error getting weather information: ${error instanceof Error ? error.message : "Unknown error"}`;
    }
  }

  private async getWeatherFromAPI(location: string, units: string): Promise<string> {
    try {
      const response = await axios.get("https://api.openweathermap.org/data/2.5/weather", {
        params: {
          q: location,
          appid: config.weatherApiKey,
          units: units
        },
        timeout: 10000
      });

      const data = response.data;
      
      const unitSymbol = units === "metric" ? "°C" : units === "imperial" ? "°F" : "K";
      const windSpeedUnit = units === "metric" ? "m/s" : "mph";
      
      const weatherInfo = `Weather in ${data.name}, ${data.sys.country}:
🌡️ Temperature: ${data.main.temp}${unitSymbol} (feels like ${data.main.feels_like}${unitSymbol})
🌤️ Condition: ${data.weather[0].main} - ${data.weather[0].description}
💧 Humidity: ${data.main.humidity}%
💨 Wind: ${data.wind.speed} ${windSpeedUnit}${data.wind.deg ? ` at ${data.wind.deg}°` : ""}
🔽 Pressure: ${data.main.pressure} hPa
👁️ Visibility: ${data.visibility ? `${data.visibility / 1000} km` : "N/A"}`;

      if (data.main.temp_min !== data.main.temp_max) {
        return weatherInfo + `\n🌡️ Range: ${data.main.temp_min}${unitSymbol} to ${data.main.temp_max}${unitSymbol}`;
      }

      return weatherInfo;

    } catch (error) {
      logger.error("Weather API error", {
        error: error instanceof Error ? error.message : error,
        location
      });
      
      if (axios.isAxiosError(error) && error.response?.status === 404) {
        throw new Error(`Location "${location}" not found. Please check the spelling and try again.`);
      }
      
      throw new Error(`Weather API request failed: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  private async simulateWeather(location: string, units: string): Promise<string> {
    // Simulate weather data when no API key is available
    const unitSymbol = units === "metric" ? "°C" : units === "imperial" ? "°F" : "K";
    const windSpeedUnit = units === "metric" ? "m/s" : "mph";
    
    // Generate some realistic-looking simulated data
    const baseTemp = units === "metric" ? 22 : units === "imperial" ? 72 : 295;
    const tempVariation = Math.random() * 20 - 10;
    const temp = Math.round((baseTemp + tempVariation) * 10) / 10;
    
    const conditions = ["Clear", "Partly Cloudy", "Cloudy", "Light Rain", "Sunny"];
    const descriptions = ["clear sky", "few clouds", "scattered clouds", "light rain", "sunny"];
    const conditionIndex = Math.floor(Math.random() * conditions.length);
    
    const humidity = Math.floor(Math.random() * 40) + 30; // 30-70%
    const windSpeed = Math.round((Math.random() * 15 + 2) * 10) / 10; // 2-17
    const pressure = Math.floor(Math.random() * 50) + 1000; // 1000-1050 hPa
    
    return `Simulated weather for ${location}:
🌡️ Temperature: ${temp}${unitSymbol} (feels like ${temp + Math.random() * 4 - 2}${unitSymbol})
🌤️ Condition: ${conditions[conditionIndex]} - ${descriptions[conditionIndex]}
💧 Humidity: ${humidity}%
💨 Wind: ${windSpeed} ${windSpeedUnit}
🔽 Pressure: ${pressure} hPa
👁️ Visibility: ${Math.floor(Math.random() * 20) + 5} km

*Note: This is simulated weather data. Configure WEATHER_API_KEY for real weather information.*`;
  }
}
