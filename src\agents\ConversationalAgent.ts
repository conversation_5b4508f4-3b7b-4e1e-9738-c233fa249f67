import { ChatOpenAI } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { ConversationChain } from "langchain/chains";
import { BufferMemory, ConversationSummaryBufferMemory } from "langchain/memory";
import { BaseAgent } from "../core/BaseAgent";
import { 
  AgentConfig, 
  AgentInput, 
  AgentOutput, 
  AgentContext,
  AgentStep,
  AgentType 
} from "../types/agent";
import { config } from "../config/environment";
import { logger } from "../utils/logger";

export class ConversationalAgent extends BaseAgent {
  private chain?: ConversationChain;
  private model: ChatOpenAI | ChatAnthropic;
  private memory: BufferMemory | ConversationSummaryBufferMemory;

  constructor(agentConfig: AgentConfig) {
    super({ ...agentConfig, type: AgentType.CONVERSATIONAL });
    this.initializeModel();
    this.initializeMemory();
  }

  private initializeModel(): void {
    const modelName = this._config.model.toLowerCase();
    
    if (modelName.includes("gpt") || modelName.includes("openai")) {
      this.model = new ChatOpenAI({
        modelName: this._config.model,
        temperature: this._config.temperature,
        openAIApiKey: config.openaiApiKey,
        maxTokens: 2000,
        timeout: this._config.timeoutMs
      });
    } else if (modelName.includes("claude") || modelName.includes("anthropic")) {
      this.model = new ChatAnthropic({
        modelName: this._config.model,
        temperature: this._config.temperature,
        anthropicApiKey: config.anthropicApiKey,
        maxTokens: 2000,
        timeout: this._config.timeoutMs
      });
    } else {
      throw new Error(`Unsupported model: ${this._config.model}`);
    }
  }

  private initializeMemory(): void {
    if (this._config.memory) {
      this.memory = this._config.memory as BufferMemory | ConversationSummaryBufferMemory;
    } else {
      // Default to summary buffer memory for better long-term conversations
      this.memory = new ConversationSummaryBufferMemory({
        llm: this.model,
        maxTokenLimit: 2000,
        returnMessages: true
      });
    }
  }

  protected async executeInternal(input: AgentInput, context: AgentContext): Promise<AgentOutput> {
    this.validateInput(input);
    
    const startTime = Date.now();
    const steps: AgentStep[] = [];
    let stepNumber = 1;

    try {
      // Initialize chain if not already done
      if (!this.chain) {
        await this.initializeChain();
      }

      logger.info("Starting Conversational agent execution", {
        agentId: this.id,
        sessionId: context.sessionId,
        model: this._config.model
      });

      // Add context loading step
      steps.push(this.createStep(
        stepNumber++,
        "load_context",
        input.message,
        undefined,
        "Loading conversation history and context",
        0,
        0
      ));

      // Load previous conversation history if provided
      if (input.history && input.history.length > 0) {
        for (const message of input.history) {
          if (message._getType() === "human") {
            await this.memory.chatMemory.addUserMessage(message.content as string);
          } else if (message._getType() === "ai") {
            await this.memory.chatMemory.addAIMessage(message.content as string);
          }
        }
      }

      // Add thinking step
      steps.push(this.createStep(
        stepNumber++,
        "thinking",
        input.message,
        undefined,
        "Considering conversation context and formulating response",
        0,
        0
      ));

      // Execute the conversation chain
      const result = await this.chain!.call({
        input: input.message
      });

      const executionTime = Date.now() - startTime;

      // Add response step
      steps.push(this.createStep(
        stepNumber++,
        "respond",
        input.message,
        result.response,
        "Generated contextual response based on conversation history",
        result.tokensUsed || 0,
        executionTime
      ));

      // Get updated conversation history
      const conversationHistory = await this.memory.chatMemory.getMessages();

      const executionResult = this.createSuccessResult(
        result.response,
        steps,
        result.tokensUsed || 0,
        executionTime,
        {
          conversationLength: conversationHistory.length,
          memoryType: this.memory.constructor.name,
          agentType: "conversational"
        }
      );

      logger.info("Conversational agent execution completed", {
        agentId: this.id,
        sessionId: context.sessionId,
        success: true,
        executionTime,
        tokensUsed: result.tokensUsed,
        conversationLength: conversationHistory.length
      });

      return {
        response: result.response,
        context,
        result: executionResult,
        conversationHistory
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      
      logger.error("Conversational agent execution failed", {
        agentId: this.id,
        sessionId: context.sessionId,
        error: errorMessage,
        executionTime
      });

      throw error;
    }
  }

  private async initializeChain(): Promise<void> {
    try {
      // Create the conversation chain
      this.chain = new ConversationChain({
        llm: this.model,
        memory: this.memory,
        verbose: this._config.verbose
      });

      // Set system prompt if provided
      if (this._config.systemPrompt) {
        // Add system message to memory
        await this.memory.chatMemory.addMessage({
          _getType: () => "system",
          content: this._config.systemPrompt!
        } as any);
      }

      logger.info("Conversational agent chain initialized", {
        agentId: this.id,
        memoryType: this.memory.constructor.name,
        hasSystemPrompt: !!this._config.systemPrompt
      });

    } catch (error) {
      logger.error("Failed to initialize Conversational agent chain", {
        agentId: this.id,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  public async clearMemory(): Promise<void> {
    await this.memory.clear();
    logger.info("Conversational agent memory cleared", {
      agentId: this.id
    });
  }

  public async getConversationHistory(): Promise<any[]> {
    return await this.memory.chatMemory.getMessages();
  }

  public updateConfig(newConfig: Partial<AgentConfig>): void {
    super.updateConfig(newConfig);
    
    // Reinitialize model if model-related config changed
    if (newConfig.model || newConfig.temperature) {
      this.initializeModel();
      this.initializeMemory(); // Memory depends on model
      this.chain = undefined; // Force re-initialization
    }
    
    // Reinitialize memory if memory config changed
    if (newConfig.memory) {
      this.initializeMemory();
      this.chain = undefined; // Force re-initialization
    }
  }
}
