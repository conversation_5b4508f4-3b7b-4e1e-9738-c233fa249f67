import { Tool } from "@langchain/core/tools";

// Tool categories
export enum ToolCategory {
  WEB_SEARCH = "web-search",
  DATABASE = "database",
  API = "api",
  FILE_SYSTEM = "file-system",
  COMPUTATION = "computation",
  COMMUNICATION = "communication",
  CUSTOM = "custom"
}

// Tool configuration
export interface ToolConfig {
  name: string;
  description: string;
  category: ToolCategory;
  enabled: boolean;
  parameters?: Record<string, any>;
  metadata?: Record<string, any>;
}

// Tool execution result
export interface ToolExecutionResult {
  success: boolean;
  result?: any;
  error?: string;
  executionTime: number;
  metadata?: Record<string, any>;
}

// Tool interfaces
export interface IToolManager {
  registerTool(tool: Tool, config: ToolConfig): void;
  unregisterTool(name: string): void;
  getTool(name: string): Tool | undefined;
  getTools(category?: ToolCategory): Tool[];
  getToolConfigs(): ToolConfig[];
  executeTool(name: string, input: any): Promise<ToolExecutionResult>;
}

// Custom tool interface
export interface ICustomTool extends Tool {
  category: ToolCategory;
  config: ToolConfig;
  validate(input: any): boolean;
  execute(input: any): Promise<ToolExecutionResult>;
}

// Tool factory interface
export interface IToolFactory {
  createTool(config: ToolConfig): Promise<ICustomTool>;
  getSupportedCategories(): ToolCategory[];
}
