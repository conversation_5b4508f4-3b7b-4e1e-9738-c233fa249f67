import { BaseMessage } from "@langchain/core/messages";

// Memory types
export enum MemoryType {
  BUFFER = "buffer",
  BUFFER_WINDOW = "buffer-window",
  SUMMARY = "summary",
  VECTOR = "vector",
  ENTITY = "entity",
  CONVERSATION_SUMMARY_BUFFER = "conversation-summary-buffer"
}

// Memory configuration
export interface MemoryConfig {
  type: MemoryType;
  maxTokens?: number;
  maxMessages?: number;
  returnMessages?: boolean;
  vectorStore?: any;
  entityStore?: any;
  summaryPrompt?: string;
  metadata?: Record<string, any>;
}

// Memory interfaces
export interface IMemoryManager {
  saveContext(sessionId: string, input: string, output: string): Promise<void>;
  loadMemoryVariables(sessionId: string): Promise<Record<string, any>>;
  getMessages(sessionId: string): Promise<BaseMessage[]>;
  clearSession(sessionId: string): Promise<void>;
  clearAll(): Promise<void>;
}

// Conversation state
export interface ConversationState {
  sessionId: string;
  userId?: string;
  messages: BaseMessage[];
  summary?: string;
  entities?: Record<string, any>;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

// Memory storage interface
export interface IMemoryStorage {
  save(sessionId: string, state: ConversationState): Promise<void>;
  load(sessionId: string): Promise<ConversationState | null>;
  delete(sessionId: string): Promise<void>;
  list(userId?: string): Promise<string[]>;
  cleanup(olderThan: Date): Promise<void>;
}
