import * as admin from "firebase-admin";
import { BaseMessage } from "@langchain/core/messages";
import { IMemoryStorage, ConversationState } from "../types/memory";
import { logger, logMemoryOperation } from "../utils/logger";

export class FirestoreMemoryStorage implements IMemoryStorage {
  private db: admin.firestore.Firestore;
  private collectionName: string;

  constructor(collectionName: string = "conversations") {
    this.db = admin.firestore();
    this.collectionName = collectionName;
  }

  async save(sessionId: string, state: ConversationState): Promise<void> {
    try {
      logMemoryOperation("save", sessionId, {
        messageCount: state.messages.length,
        hasEntities: !!state.entities,
        hasSummary: !!state.summary
      });

      // Convert messages to serializable format
      const serializedMessages = state.messages.map(msg => ({
        type: msg._getType(),
        content: msg.content,
        additional_kwargs: msg.additional_kwargs || {},
        timestamp: new Date()
      }));

      const docData = {
        sessionId: state.sessionId,
        userId: state.userId,
        messages: serializedMessages,
        summary: state.summary,
        entities: state.entities || {},
        metadata: state.metadata || {},
        createdAt: admin.firestore.Timestamp.fromDate(state.createdAt),
        updatedAt: admin.firestore.Timestamp.fromDate(state.updatedAt)
      };

      await this.db.collection(this.collectionName).doc(sessionId).set(docData);

      logger.info("Conversation state saved to Firestore", {
        sessionId,
        messageCount: state.messages.length
      });

    } catch (error) {
      logger.error("Failed to save conversation state", {
        sessionId,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  async load(sessionId: string): Promise<ConversationState | null> {
    try {
      logMemoryOperation("load", sessionId, {});

      const doc = await this.db.collection(this.collectionName).doc(sessionId).get();
      
      if (!doc.exists) {
        logger.info("Conversation state not found", { sessionId });
        return null;
      }

      const data = doc.data()!;
      
      // Convert serialized messages back to BaseMessage objects
      const messages: BaseMessage[] = (data.messages || []).map((msgData: any) => {
        return {
          _getType: () => msgData.type,
          content: msgData.content,
          additional_kwargs: msgData.additional_kwargs || {}
        } as BaseMessage;
      });

      const state: ConversationState = {
        sessionId: data.sessionId,
        userId: data.userId,
        messages,
        summary: data.summary,
        entities: data.entities || {},
        metadata: data.metadata || {},
        createdAt: data.createdAt.toDate(),
        updatedAt: data.updatedAt.toDate()
      };

      logger.info("Conversation state loaded from Firestore", {
        sessionId,
        messageCount: messages.length
      });

      return state;

    } catch (error) {
      logger.error("Failed to load conversation state", {
        sessionId,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  async delete(sessionId: string): Promise<void> {
    try {
      logMemoryOperation("delete", sessionId, {});

      await this.db.collection(this.collectionName).doc(sessionId).delete();

      logger.info("Conversation state deleted from Firestore", { sessionId });

    } catch (error) {
      logger.error("Failed to delete conversation state", {
        sessionId,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  async list(userId?: string): Promise<string[]> {
    try {
      let query: admin.firestore.Query = this.db.collection(this.collectionName);
      
      if (userId) {
        query = query.where("userId", "==", userId);
      }

      const snapshot = await query.select("sessionId").get();
      const sessionIds = snapshot.docs.map(doc => doc.data().sessionId);

      logger.info("Listed conversation sessions", {
        userId,
        count: sessionIds.length
      });

      return sessionIds;

    } catch (error) {
      logger.error("Failed to list conversation sessions", {
        userId,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  async cleanup(olderThan: Date): Promise<void> {
    try {
      logMemoryOperation("cleanup", "system", { olderThan });

      const cutoffTimestamp = admin.firestore.Timestamp.fromDate(olderThan);
      
      // Query for old conversations
      const query = this.db.collection(this.collectionName)
        .where("updatedAt", "<", cutoffTimestamp)
        .limit(500); // Process in batches

      const snapshot = await query.get();
      
      if (snapshot.empty) {
        logger.info("No old conversations to cleanup");
        return;
      }

      // Delete in batch
      const batch = this.db.batch();
      snapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });

      await batch.commit();

      logger.info("Cleaned up old conversations", {
        deletedCount: snapshot.docs.length,
        olderThan
      });

      // If we deleted the maximum batch size, there might be more to clean
      if (snapshot.docs.length === 500) {
        await this.cleanup(olderThan);
      }

    } catch (error) {
      logger.error("Failed to cleanup old conversations", {
        olderThan,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  async getStats(): Promise<{ totalConversations: number; totalMessages: number }> {
    try {
      const snapshot = await this.db.collection(this.collectionName).get();
      
      let totalMessages = 0;
      snapshot.docs.forEach(doc => {
        const data = doc.data();
        totalMessages += (data.messages || []).length;
      });

      return {
        totalConversations: snapshot.size,
        totalMessages
      };

    } catch (error) {
      logger.error("Failed to get memory storage stats", {
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }
}
