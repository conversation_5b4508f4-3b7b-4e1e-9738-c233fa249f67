{"name": "@langchain/anthropic", "version": "0.3.24", "description": "Anthropic integrations for LangChain.js", "type": "module", "engines": {"node": ">=18"}, "main": "./index.js", "types": "./index.d.ts", "repository": {"type": "git", "url": "**************:langchain-ai/langchainjs.git"}, "homepage": "https://github.com/langchain-ai/langchainjs/tree/main/libs/langchain-anthropic/", "scripts": {"build": "yarn turbo:command build:internal --filter=@langchain/anthropic", "build:internal": "yarn lc_build --create-entrypoints --pre --tree-shaking --gen-maps", "lint:eslint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint:dpdm": "dpdm --skip-dynamic-imports circular --exit-code circular:1 --no-warning --no-tree src/*.ts src/**/*.ts", "lint": "yarn lint:eslint && yarn lint:dpdm", "lint:fix": "yarn lint:eslint --fix && yarn lint:dpdm", "clean": "rm -rf .turbo dist/", "prepack": "yarn build", "test": "NODE_OPTIONS=--experimental-vm-modules jest --testPathIgnorePatterns=\\.int\\.test.ts --testTimeout 30000 --maxWorkers=50%", "test:watch": "NODE_OPTIONS=--experimental-vm-modules jest --watch --testPathIgnorePatterns=\\.int\\.test.ts", "test:single": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.cjs --testTimeout 100000", "test:int": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:standard:unit": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.standard\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:standard:int": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.standard\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:standard": "yarn test:standard:unit && yarn test:standard:int", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\""}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"@anthropic-ai/sdk": "^0.56.0", "fast-xml-parser": "^4.4.1"}, "peerDependencies": {"@langchain/core": ">=0.3.58 <0.4.0"}, "devDependencies": {"@anthropic-ai/vertex-sdk": "^0.11.5", "@jest/globals": "^29.5.0", "@langchain/core": "workspace:*", "@langchain/scripts": ">=0.1.0 <0.2.0", "@langchain/standard-tests": "0.0.0", "@swc/core": "^1.3.90", "@swc/jest": "^0.2.29", "dpdm": "^3.14.0", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "jest-environment-node": "^29.6.4", "prettier": "^2.8.3", "release-it": "^18.1.2", "rimraf": "^5.0.1", "ts-jest": "^29.1.0", "typescript": "~5.8.3", "zod": "^3.25.32"}, "publishConfig": {"access": "public"}, "keywords": ["llm", "ai", "gpt3", "chain", "prompt", "prompt engineering", "chatgpt", "machine learning", "ml", "anthropic", "embeddings", "vectorstores"], "exports": {".": {"types": {"import": "./index.d.ts", "require": "./index.d.cts", "default": "./index.d.ts"}, "import": "./index.js", "require": "./index.cjs"}, "./experimental": {"types": {"import": "./experimental.d.ts", "require": "./experimental.d.cts", "default": "./experimental.d.ts"}, "import": "./experimental.js", "require": "./experimental.cjs"}, "./package.json": "./package.json"}, "files": ["dist/", "index.cjs", "index.js", "index.d.ts", "index.d.cts", "experimental.cjs", "experimental.js", "experimental.d.ts", "experimental.d.cts"]}