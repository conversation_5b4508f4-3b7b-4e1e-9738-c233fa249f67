{"name": "@langchain/openai", "version": "0.3.17", "description": "OpenAI integrations for LangChain.js", "type": "module", "engines": {"node": ">=18"}, "main": "./index.js", "types": "./index.d.ts", "repository": {"type": "git", "url": "**************:langchain-ai/langchainjs.git"}, "homepage": "https://github.com/langchain-ai/langchainjs/tree/main/libs/langchain-openai/", "scripts": {"build": "yarn turbo:command build:internal --filter=@langchain/openai", "build:internal": "yarn lc_build --create-entrypoints --pre --tree-shaking", "lint:eslint": "NODE_OPTIONS=--max-old-space-size=4096 eslint --cache --ext .ts,.js src/", "lint:dpdm": "dpdm --exit-code circular:1 --no-warning --no-tree src/*.ts src/**/*.ts", "lint": "yarn lint:eslint && yarn lint:dpdm", "lint:fix": "yarn lint:eslint --fix && yarn lint:dpdm", "clean": "rm -rf .turbo dist/", "prepack": "yarn build", "test": "NODE_OPTIONS=--experimental-vm-modules jest --testPathIgnorePatterns=\\.int\\.test.ts --testTimeout 30000 --maxWorkers=50%", "test:watch": "NODE_OPTIONS=--experimental-vm-modules jest --watch --testPathIgnorePatterns=\\.int\\.test.ts", "test:single": "NODE_OPTIONS=--experimental-vm-modules yarn run jest --config jest.config.cjs --testTimeout 100000", "test:int": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:standard:unit": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.standard\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:standard:int": "NODE_OPTIONS=--experimental-vm-modules jest --testPathPattern=\\.standard\\.int\\.test.ts --testTimeout 100000 --maxWorkers=50%", "test:standard": "yarn test:standard:unit && yarn test:standard:int", "format": "prettier --config .prettierrc --write \"src\"", "format:check": "prettier --config .prettierrc --check \"src\""}, "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "dependencies": {"js-tiktoken": "^1.0.12", "openai": "^4.77.0", "zod": "^3.22.4", "zod-to-json-schema": "^3.22.3"}, "peerDependencies": {"@langchain/core": ">=0.3.29 <0.4.0"}, "devDependencies": {"@azure/identity": "^4.2.1", "@jest/globals": "^29.5.0", "@langchain/core": "workspace:*", "@langchain/scripts": ">=0.1.0 <0.2.0", "@langchain/standard-tests": "0.0.0", "@swc/core": "^1.3.90", "@swc/jest": "^0.2.29", "dpdm": "^3.12.0", "eslint": "^8.33.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-prettier": "^8.6.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jest": "^27.6.0", "eslint-plugin-no-instanceof": "^1.0.1", "eslint-plugin-prettier": "^4.2.1", "jest": "^29.5.0", "jest-environment-node": "^29.6.4", "prettier": "^2.8.3", "release-it": "^17.6.0", "rimraf": "^5.0.1", "ts-jest": "^29.1.0", "typescript": "~5.1.6"}, "publishConfig": {"access": "public"}, "keywords": ["llm", "ai", "gpt3", "chain", "prompt", "prompt engineering", "chatgpt", "machine learning", "ml", "openai", "embeddings", "vectorstores"], "exports": {".": {"types": {"import": "./index.d.ts", "require": "./index.d.cts", "default": "./index.d.ts"}, "import": "./index.js", "require": "./index.cjs"}, "./package.json": "./package.json"}, "files": ["dist/", "index.cjs", "index.js", "index.d.ts", "index.d.cts"]}