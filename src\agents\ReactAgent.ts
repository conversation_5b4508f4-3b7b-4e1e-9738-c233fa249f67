import { ChatOpenAI } from "@langchain/openai";
import { ChatAnthropic } from "@langchain/anthropic";
import { AgentExecutor, createReactAgent } from "langchain/agents";
import { pull } from "langchain/hub";
import { BaseAgent } from "../core/BaseAgent";
import { 
  AgentConfig, 
  AgentInput, 
  AgentOutput, 
  AgentContext,
  AgentStep,
  AgentType 
} from "../types/agent";
import { config } from "../config/environment";
import { logger } from "../utils/logger";

export class ReactAgent extends BaseAgent {
  private executor?: AgentExecutor;
  private model: ChatOpenAI | ChatAnthropic;

  constructor(agentConfig: AgentConfig) {
    super({ ...agentConfig, type: AgentType.REACT });
    this.initializeModel();
  }

  private initializeModel(): void {
    const modelName = this._config.model.toLowerCase();
    
    if (modelName.includes("gpt") || modelName.includes("openai")) {
      this.model = new ChatOpenAI({
        modelName: this._config.model,
        temperature: this._config.temperature,
        openAIApiKey: config.openaiApiKey,
        maxTokens: 2000,
        timeout: this._config.timeoutMs
      });
    } else if (modelName.includes("claude") || modelName.includes("anthropic")) {
      this.model = new ChatAnthropic({
        modelName: this._config.model,
        temperature: this._config.temperature,
        anthropicApiKey: config.anthropicApiKey,
        maxTokens: 2000,
        timeout: this._config.timeoutMs
      });
    } else {
      throw new Error(`Unsupported model: ${this._config.model}`);
    }
  }

  protected async executeInternal(input: AgentInput, context: AgentContext): Promise<AgentOutput> {
    this.validateInput(input);
    
    const startTime = Date.now();
    const steps: AgentStep[] = [];
    let stepNumber = 1;

    try {
      // Initialize executor if not already done
      if (!this.executor) {
        await this.initializeExecutor();
      }

      logger.info("Starting ReAct agent execution", {
        agentId: this.id,
        sessionId: context.sessionId,
        model: this._config.model,
        maxIterations: this._config.maxIterations
      });

      // Add thinking step
      steps.push(this.createStep(
        stepNumber++,
        "thinking",
        input.message,
        undefined,
        "Analyzing the user's request and planning my approach",
        0,
        0
      ));

      // Execute the agent
      const result = await this.executor!.invoke({
        input: input.message,
        chat_history: input.history || []
      });

      // Add execution step
      const executionTime = Date.now() - startTime;
      steps.push(this.createStep(
        stepNumber++,
        "execute",
        input.message,
        result.output,
        "Executed ReAct reasoning and action cycle",
        result.tokensUsed || 0,
        executionTime
      ));

      const executionResult = this.createSuccessResult(
        result.output,
        steps,
        result.tokensUsed || 0,
        executionTime,
        {
          intermediateSteps: result.intermediateSteps,
          agentType: "react"
        }
      );

      logger.info("ReAct agent execution completed", {
        agentId: this.id,
        sessionId: context.sessionId,
        success: true,
        executionTime,
        tokensUsed: result.tokensUsed
      });

      return {
        response: result.output,
        context,
        result: executionResult
      };

    } catch (error) {
      const executionTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      
      logger.error("ReAct agent execution failed", {
        agentId: this.id,
        sessionId: context.sessionId,
        error: errorMessage,
        executionTime
      });

      throw error;
    }
  }

  private async initializeExecutor(): Promise<void> {
    try {
      // Get the ReAct prompt from LangChain Hub
      const prompt = await pull("hwchase17/react");
      
      // Create the ReAct agent
      const agent = await createReactAgent({
        llm: this.model,
        tools: this._config.tools || [],
        prompt: prompt
      });

      // Create the executor
      this.executor = new AgentExecutor({
        agent,
        tools: this._config.tools || [],
        maxIterations: this._config.maxIterations,
        verbose: this._config.verbose,
        returnIntermediateSteps: true,
        handleParsingErrors: true
      });

      logger.info("ReAct agent executor initialized", {
        agentId: this.id,
        toolCount: this._config.tools?.length || 0,
        maxIterations: this._config.maxIterations
      });

    } catch (error) {
      logger.error("Failed to initialize ReAct agent executor", {
        agentId: this.id,
        error: error instanceof Error ? error.message : error
      });
      throw error;
    }
  }

  public updateConfig(newConfig: Partial<AgentConfig>): void {
    super.updateConfig(newConfig);
    
    // Reinitialize model if model-related config changed
    if (newConfig.model || newConfig.temperature) {
      this.initializeModel();
      this.executor = undefined; // Force re-initialization
    }
    
    // Reinitialize executor if tools or iterations changed
    if (newConfig.tools || newConfig.maxIterations) {
      this.executor = undefined; // Force re-initialization
    }
  }
}
