import { Tool } from "@langchain/core/tools";
import { z } from "zod";
import { logger } from "../utils/logger";

const calculatorSchema = z.object({
  expression: z.string().describe("Mathematical expression to evaluate (e.g., '2 + 3 * 4', 'sqrt(16)', 'sin(pi/2)')"),
});

export class CalculatorTool extends Tool {
  name = "calculator";
  description = "Perform mathematical calculations. Supports basic arithmetic, trigonometric functions, logarithms, and more. Use this for any mathematical computations.";
  schema = calculatorSchema;

  constructor() {
    super();
  }

  async _call(input: z.infer<typeof calculatorSchema>): Promise<string> {
    try {
      const { expression } = input;
      
      logger.info("Calculator tool called", { expression });

      // Sanitize the expression to prevent code injection
      const sanitizedExpression = this.sanitizeExpression(expression);
      
      if (!sanitizedExpression) {
        return "Error: Invalid mathematical expression. Please use only mathematical operators and functions.";
      }

      // Evaluate the expression safely
      const result = this.evaluateExpression(sanitizedExpression);
      
      logger.info("Calculator tool result", { expression, result });
      
      return `${expression} = ${result}`;

    } catch (error) {
      logger.error("Calculator tool error", {
        error: error instanceof Error ? error.message : error,
        input
      });
      
      return `Error calculating "${input.expression}": ${error instanceof Error ? error.message : "Invalid expression"}`;
    }
  }

  private sanitizeExpression(expression: string): string | null {
    // Remove whitespace
    const cleaned = expression.replace(/\s+/g, "");
    
    // Allow only mathematical characters and functions
    const allowedPattern = /^[0-9+\-*/().^%\s,a-zA-Z]*$/;
    
    if (!allowedPattern.test(cleaned)) {
      return null;
    }

    // Replace common mathematical functions and constants
    let sanitized = cleaned
      .replace(/\bpi\b/g, "Math.PI")
      .replace(/\be\b/g, "Math.E")
      .replace(/\bsin\(/g, "Math.sin(")
      .replace(/\bcos\(/g, "Math.cos(")
      .replace(/\btan\(/g, "Math.tan(")
      .replace(/\basin\(/g, "Math.asin(")
      .replace(/\bacos\(/g, "Math.acos(")
      .replace(/\batan\(/g, "Math.atan(")
      .replace(/\batan2\(/g, "Math.atan2(")
      .replace(/\bsinh\(/g, "Math.sinh(")
      .replace(/\bcosh\(/g, "Math.cosh(")
      .replace(/\btanh\(/g, "Math.tanh(")
      .replace(/\bsqrt\(/g, "Math.sqrt(")
      .replace(/\bcbrt\(/g, "Math.cbrt(")
      .replace(/\babs\(/g, "Math.abs(")
      .replace(/\bfloor\(/g, "Math.floor(")
      .replace(/\bceil\(/g, "Math.ceil(")
      .replace(/\bround\(/g, "Math.round(")
      .replace(/\bmin\(/g, "Math.min(")
      .replace(/\bmax\(/g, "Math.max(")
      .replace(/\bpow\(/g, "Math.pow(")
      .replace(/\bexp\(/g, "Math.exp(")
      .replace(/\blog\(/g, "Math.log(")
      .replace(/\blog10\(/g, "Math.log10(")
      .replace(/\blog2\(/g, "Math.log2(")
      .replace(/\brandom\(/g, "Math.random(")
      .replace(/\^/g, "**"); // Convert ^ to ** for exponentiation

    return sanitized;
  }

  private evaluateExpression(expression: string): number {
    try {
      // Use Function constructor for safer evaluation than eval
      const func = new Function("Math", `"use strict"; return (${expression})`);
      const result = func(Math);
      
      if (typeof result !== "number" || !isFinite(result)) {
        throw new Error("Result is not a finite number");
      }
      
      // Round to reasonable precision to avoid floating point issues
      return Math.round(result * 1e12) / 1e12;
      
    } catch (error) {
      throw new Error(`Invalid mathematical expression: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }
}
